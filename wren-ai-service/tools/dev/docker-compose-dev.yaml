version: "3.8"

volumes:
  data:

networks:
  wren:
    driver: bridge

services:
  wren-engine:
    image: ghcr.io/canner/wren-engine:${WREN_ENGINE_VERSION}
    pull_policy: always
    platform: ${PLATFORM}
    ports:
      - ${WREN_ENGINE_SQL_PORT}:${WREN_ENGINE_SQL_PORT}
      - ${WREN_ENGINE_PORT}:${WREN_ENGINE_PORT}
    volumes:
      - ./etc:/usr/src/app/etc
    networks:
      - wren

  ibis-server:
    image: ghcr.io/canner/wren-engine-ibis:${IBIS_SERVER_VERSION}
    pull_policy: always
    platform: ${PLATFORM}
    ports:
      - ${IBIS_SERVER_PORT}:${IBIS_SERVER_PORT}
    environment:
      WREN_ENGINE_ENDPOINT: http://wren-engine:${WREN_ENGINE_PORT}
      LOG_LEVEL: DEBUG
    networks:
      - wren

  qdrant:
    image: qdrant/qdrant:v1.11.0
    pull_policy: always
    ports:
      - 6333:6333
      - 6334:6334
    networks:
      - wren

  wren-ui:
    image: ghcr.io/canner/wren-ui:${WREN_UI_VERSION}
    pull_policy: always
    platform: ${PLATFORM}
    environment:
      DB_TYPE: sqlite
      # /app is the working directory in the container
      SQLITE_FILE: /app/data/db.sqlite3
      WREN_ENGINE_ENDPOINT: http://wren-engine:${WREN_ENGINE_PORT}
      WREN_AI_ENDPOINT: http://host.docker.internal:${WREN_AI_SERVICE_PORT}
      IBIS_SERVER_ENDPOINT: http://ibis-server:${IBIS_SERVER_PORT}
      GENERATION_MODEL: ${GENERATION_MODEL}
      # telemetry
      WREN_ENGINE_PORT: ${WREN_ENGINE_PORT}
      WREN_AI_SERVICE_VERSION: ${WREN_AI_SERVICE_VERSION}
      WREN_UI_VERSION: ${WREN_UI_VERSION}
      WREN_ENGINE_VERSION: ${WREN_ENGINE_VERSION}
      USER_UUID: ${USER_UUID}
      POSTHOG_API_KEY: ${POSTHOG_API_KEY}
      POSTHOG_HOST: ${POSTHOG_HOST}
      TELEMETRY_ENABLED: ${TELEMETRY_ENABLED}
      # client side
      NEXT_PUBLIC_USER_UUID: ${USER_UUID}
      NEXT_PUBLIC_POSTHOG_API_KEY: ${POSTHOG_API_KEY}
      NEXT_PUBLIC_POSTHOG_HOST: ${POSTHOG_HOST}
      NEXT_PUBLIC_TELEMETRY_ENABLED: ${TELEMETRY_ENABLED}
      # configs
      WREN_PRODUCT_VERSION: ${WREN_PRODUCT_VERSION}

    ports:
      - ${WREN_UI_PORT}:3000
    volumes:
      - ./etc:/app/data
    networks:
      - wren
    depends_on:
      - wren-engine
      - ibis-server

  postgres:
    image: postgres:14-alpine
    platform: ${PLATFORM}
    ports:
      - 5432:5432
    volumes:
      - data:/var/lib/postgresql/data
    environment:
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_DB=test
      - PGDATA=/var/lib/postgresql/data/pgdata
    networks:
      - wren
