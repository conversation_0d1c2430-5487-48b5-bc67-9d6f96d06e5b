import logging
import sys
from typing import Any, Optional, Dict

import or<PERSON><PERSON>
from hamilton import base
from hamilton.async_driver import AsyncDriver
from haystack.components.builders.prompt_builder import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langfuse.decorators import observe
from pydantic import BaseModel

from src.core.engine import Engine, add_quotes
from src.core.pipeline import BasicPipeline
from src.core.provider import LLMProvider
from src.pipelines.generation.utils.sql import (
    SQLGenPostProcessor,
    TEXT_TO_SQL_RULES,
    calculated_field_instructions,
    metric_instructions,
    sql_samples_instructions,
    construct_instructions,
)
from src.pipelines.retrieval.sql_executor import SQLExecutor
from functools import lru_cache
from typing import Tuple
import hashlib

logger = logging.getLogger("wren-ai-service")

# Global reference for status updates
_monitor_status_updater = None

# Simple in-memory cache for schema retrieval with TTL
_schema_cache = {}
_SCHEMA_CACHE_TTL = 300  # 5 minutes


def _get_cache_key(original_message: str, existing_sql: str, project_id: str) -> str:
    """Generate a cache key for schema retrieval"""
    content = f"{project_id}:{original_message}:{existing_sql}"
    return hashlib.md5(content.encode()).hexdigest()

## Start of Pipeline

@observe(capture_input=False)
async def schema_retrieval_for_monitor(
    original_message: str,
    existing_sql: str,
    project_id: Optional[str] = None,
    embedder: Any = None,
    document_store: Any = None,
) -> dict:
    """
    Retrieve schema information for monitor generation using the same approach as the ask pipeline.
    This provides table descriptions, column information, and data types for accurate SQL generation.
    Uses caching to avoid duplicate retrievals within the same pipeline execution.
    """
    # Update status to understanding (schema retrieval phase)
    global _monitor_status_updater
    if _monitor_status_updater:
        _monitor_status_updater("understanding")
    
    try:
        if not embedder or not document_store:
            logger.warning("No embedder or document_store available for schema retrieval")
            return {"schema_context": ""}
        
        # Check cache first
        cache_key = _get_cache_key(original_message, existing_sql, project_id or "")
        cached_result = _schema_cache.get(cache_key)
        
        if cached_result:
            import time
            if time.time() - cached_result["timestamp"] < _SCHEMA_CACHE_TTL:
                logger.info(f"Using cached schema context for key {cache_key[:8]}...")
                return {"schema_context": cached_result["schema_context"]}
        
        # Create a query that combines the original message and existing SQL for schema retrieval
        schema_query = f"{original_message}\n\nExisting SQL for context:\n{existing_sql}"
        
        # Generate embedding for the query
        embedding_result = await embedder.run(schema_query)
        
        # Retrieve relevant schema documents
        filters = {
            "operator": "AND",
            "conditions": [
                {"field": "type", "operator": "==", "value": "TABLE_DESCRIPTION"},
                {"field": "project_id", "operator": "==", "value": project_id}
            ],
        }
        
        retrieval_result = await document_store.run(
            query_embedding=embedding_result.get("embedding"),
            filters=filters,
            top_k=5,  # Limit to top 5 relevant tables
        )
        
        # Extract schema context from retrieved documents
        schema_lines = []
        documents = retrieval_result.get("documents", [])
        
        for doc in documents:
            if hasattr(doc, 'content') and hasattr(doc, 'meta'):
                try:
                    # Parse document content (similar to ask pipeline)
                    content = doc.content
                    if isinstance(content, str):
                        import ast
                        content = ast.literal_eval(content)
                    
                    if content.get("type") == "TABLE" and content.get("columns"):
                        table_name = doc.meta.get("name", "unknown_table")
                        columns = content.get("columns", [])
                        
                        column_lines = []
                        for col in columns:
                            if col.get("type") == "COLUMN":
                                col_line = f"  {col.get('name', 'unknown')} {col.get('data_type', 'unknown')}"
                                if col.get("is_primary_key"):
                                    col_line += " PRIMARY KEY"
                                column_lines.append(col_line)
                        
                        if column_lines:
                            schema_lines.append(f"Table: {table_name}\n" + "\n".join(column_lines))
                
                except Exception as e:
                    logger.warning(f"Error parsing schema document: {e}")
                    continue
        
        schema_context = "\n\n".join(schema_lines) if schema_lines else ""
        
        # Cache the result
        import time
        _schema_cache[cache_key] = {
            "schema_context": schema_context,
            "timestamp": time.time()
        }
        
        logger.info(f"Schema retrieval completed. Found {len(schema_lines)} relevant tables. Cached with key {cache_key[:8]}...")
        return {"schema_context": schema_context}
        
    except Exception as e:
        logger.error(f"Error in schema retrieval for monitor: {e}")
        return {"schema_context": ""}


## Main Pipeline Stages

@observe(capture_input=False)
def reasoning_prompt(
    original_message: str,
    sql_reasoning: str,
    existing_sql: str,
    clarification_prompt: str,
    thread_data: Dict[str, Any],
    prompt_builder: PromptBuilder,
) -> dict:
    # Extract additional context from thread data
    answer_detail = thread_data.get("answerDetail", {})
    chart_detail = thread_data.get("chartDetail", {})
    breakdown_detail = thread_data.get("breakdownDetail", {})
    schema_context = thread_data.get("schemaContext", "")

    answer_content = answer_detail.get("content", "") if answer_detail else ""
    chart_description = chart_detail.get("description", "") if chart_detail else ""
    chart_type = chart_detail.get("chartType", "") if chart_detail else ""

    # Build context from breakdown steps
    breakdown_context = ""
    if breakdown_detail and breakdown_detail.get("steps"):
        breakdown_context = "\n".join([
            f"- {step.get('summary', '')}: {step.get('sql', '')}"
            for step in breakdown_detail.get("steps", [])
        ])

    user_prompt = f"""
You are an expert data monitoring specialist. Your task is to analyze a user's question and the comprehensive response data to understand what business metric or pattern should be monitored.

**Original User Question:**
{original_message}

**SQL Query Generated:**
```sql
{existing_sql}
```

**Database Schema Context:**
{schema_context if schema_context else "No schema context available - using existing SQL structure for reference."}

**AI-Generated Answer Context:**
{answer_content}

**Chart Visualization Context:**
{f"Chart Type: {chart_type}" if chart_type else ""}
{f"Chart Description: {chart_description}" if chart_description else ""}

**Query Breakdown Steps:**
{breakdown_context if breakdown_context else "No breakdown steps available"}

**Additional Clarification:**
{clarification_prompt or "None provided"}

**Your Task:**
Analyze all this context to understand the business intent and determine what should be monitored. Use the schema context to understand the available tables, columns, and their data types. Provide:

1. **Business Context Analysis**: What business metric or pattern is the user trying to track?
2. **Monitoring Approach**: How should this be monitored (what changes would be significant)?
3. **Key Insights**: What insights from the answer/chart should inform the monitoring strategy?

**Response Format:**
Return a JSON object with:
{{
    "business_context": "Description of what business metric/pattern to monitor",
    "monitoring_approach": "How this should be monitored and what changes matter",
    "key_insights": ["insight1", "insight2", "insight3"],
    "recommended_aggregation": "COUNT|SUM|AVG|MAX|MIN|etc based on the data pattern"
}}
"""

    return prompt_builder.run(prompt=user_prompt)


@observe(as_type="generation", capture_input=False)
async def reasoning_generation(
    reasoning_prompt: dict, generator: Any, query_id: str, generator_name: str
) -> dict:
    # Update status to reasoning
    global _monitor_status_updater
    if _monitor_status_updater:
        _monitor_status_updater("reasoning")
    
    result = await generator(
        prompt=reasoning_prompt.get("prompt"), query_id=query_id
    )
    return result


@observe(capture_input=False)
def reasoning_post_process(reasoning_generation: dict) -> dict:
    try:
        response_data = reasoning_generation.get("replies")
        if not response_data or len(response_data) == 0:
            raise ValueError("No response from LLM")

        result = orjson.loads(response_data[0])

        required_fields = ["business_context", "monitoring_approach", "key_insights", "recommended_aggregation"]
        for field in required_fields:
            if field not in result:
                raise ValueError(f"Missing required field: {field}")

        return {
            "business_context": result["business_context"],
            "monitoring_approach": result["monitoring_approach"],
            "key_insights": result["key_insights"],
            "recommended_aggregation": result["recommended_aggregation"],
        }
    except Exception as e:
        return {
            "business_context": f"Error processing reasoning: {str(e)}",
            "monitoring_approach": "Unable to determine monitoring approach",
            "key_insights": [f"Processing error: {str(e)}"],
            "recommended_aggregation": "COUNT",
        }


@observe(capture_input=False)
def sql_generation_prompt(
    original_message: str,
    existing_sql: str,
    reasoning_post_process: dict,
    clarification_prompt: str,
    thread_data: Dict[str, Any],
    prompt_builder: PromptBuilder,
) -> dict:
    business_context = reasoning_post_process.get("business_context", "")
    monitoring_approach = reasoning_post_process.get("monitoring_approach", "")
    recommended_aggregation = reasoning_post_process.get("recommended_aggregation", "COUNT")
    
    # Extract schema context from thread data
    schema_context = thread_data.get("schemaContext", "")

    user_prompt = f"""
You are an expert SQL developer specializing in creating comparative monitoring queries. Based on the business analysis, create a monitoring SQL query that performs dynamic threshold comparisons.

**Business Context:**
{business_context}

**Monitoring Approach:**
{monitoring_approach}

**Original SQL Query:**
```sql
{existing_sql}
```

**Database Schema Context:**
{schema_context if schema_context else "No schema context available - use existing SQL structure for reference."}

**Recommended Aggregation:**
{recommended_aggregation}

**Additional Requirements:**
{clarification_prompt or "None"}

{TEXT_TO_SQL_RULES}

{calculated_field_instructions}

{metric_instructions}

**Your Task:**
Transform the original SQL into a comparative monitoring query that:
1. STRICTLY FOLLOW the SQL RULES above for proper syntax and structure
2. Uses the database schema context to ensure accurate column names and data types
3. Includes baseline calculations for comparison (previous period, rolling average, etc.)
4. Returns current value, baseline value, and comparison metrics
5. Uses CTEs to structure the comparison logic clearly
6. Supports percentage change, absolute difference, or ratio comparisons
7. Can be executed regularly to detect threshold violations
8. Validates column names and table references against the provided schema
9. ALWAYS QUALIFY column names with their table name or table alias to avoid ambiguity
10. Use proper time zone casting with TIMESTAMP WITH TIME ZONE for date comparisons
11. Use CROSS JOIN syntax instead of comma joins for CTE combinations
12. Always use table aliases when referencing CTE columns (e.g., cp.current_value not current_period.current_value)
13. Ensure all CTE column names are unique and properly aliased

**Comparative SQL Pattern Example:**
```sql
WITH current_period AS (
  SELECT {recommended_aggregation}(...) as current_value
  FROM ...
  WHERE CAST(date_column AS TIMESTAMP WITH TIME ZONE) >= CAST(CURRENT_DATE - INTERVAL '30' DAY AS TIMESTAMP WITH TIME ZONE)
),
baseline_period AS (
  SELECT {recommended_aggregation}(...) as baseline_value
  FROM ...
  WHERE CAST(date_column AS TIMESTAMP WITH TIME ZONE) >= CAST(CURRENT_DATE - INTERVAL '60' DAY AS TIMESTAMP WITH TIME ZONE)
    AND CAST(date_column AS TIMESTAMP WITH TIME ZONE) < CAST(CURRENT_DATE - INTERVAL '30' DAY AS TIMESTAMP WITH TIME ZONE)
)
SELECT
  cp.current_value,
  bp.baseline_value,
  ((cp.current_value - bp.baseline_value) / NULLIF(bp.baseline_value, 0) * 100) as percentage_change,
  (cp.current_value - bp.baseline_value) as absolute_difference,
  (cp.current_value / NULLIF(bp.baseline_value, 0)) as ratio
FROM current_period cp
CROSS JOIN baseline_period bp
```

**Response Format:**
Return a JSON object with:
{{
    "monitor_sql": "WITH current_period AS (...) SELECT current_value, baseline_value, percentage_change FROM ...",
    "explanation": "Clear explanation of what this query monitors and the comparison logic"
}}
"""

    return prompt_builder.run(prompt=user_prompt)


@observe(as_type="generation", capture_input=False)
async def sql_generation(
    sql_generation_prompt: dict, sql_generator: Any, query_id: str, generator_name: str
) -> dict:
    # Update status to generating
    global _monitor_status_updater
    if _monitor_status_updater:
        _monitor_status_updater("generating")
    
    result = await sql_generator(
        prompt=sql_generation_prompt.get("prompt"), query_id=query_id
    )
    return result


@observe(as_type="generation", capture_input=False)
async def ai_sql_analysis(
    ai_sql_analysis_prompt: dict, ai_sql_analyzer: Any, query_id: str, generator_name: str
) -> dict:
    result = await ai_sql_analyzer(
        prompt=ai_sql_analysis_prompt.get("prompt"), query_id=query_id
    )
    return result


@observe(as_type="generation", capture_input=False)
async def sql_regeneration(
    sql_regeneration_prompt: dict, sql_regenerator: Any, query_id: str, generator_name: str
) -> dict:
    result = await sql_regenerator(
        prompt=sql_regeneration_prompt.get("prompt"), query_id=query_id
    )
    return result


@observe(capture_input=False)
async def sql_post_process(
    sql_generation: dict,
    post_processor: SQLGenPostProcessor,
    engine_timeout: float,
    project_id: str | None = None,
    use_dry_plan: bool = False,
    data_source: str = "",
) -> dict:
    try:
        response_data = sql_generation.get("replies")
        if not response_data or len(response_data) == 0:
            raise ValueError("No response from LLM")

        # Parse the JSON response to extract monitor SQL and other fields
        result = orjson.loads(response_data[0])

        required_fields = ["monitor_sql", "explanation"]

        for field in required_fields:
            if field not in result:
                raise ValueError(f"Missing required field: {field}")

        monitor_sql = result["monitor_sql"]

        # Use the comprehensive SQLGenPostProcessor for proper validation
        # This applies all SQL rules and transformations from sql.py
        logger.info(f"Running SQLGenPostProcessor for monitor SQL validation")
        
        # Use a shorter timeout for validation to prevent long waits
        validation_timeout = min(engine_timeout, 30.0)  # Max 30 seconds for validation
        
        validation_result = await post_processor.run(
            replies=[monitor_sql],
            timeout=validation_timeout,
            project_id=project_id,
            use_dry_plan=use_dry_plan,
            data_source=data_source,
        )
        
        valid_generation_result = validation_result.get("valid_generation_result", {})
        invalid_generation_result = validation_result.get("invalid_generation_result", {})
        
        if valid_generation_result and valid_generation_result.get("sql"):
            # SQL is valid and properly processed
            return {
                "monitor_sql": valid_generation_result["sql"],
                "explanation": result["explanation"],
                "sql_validation_successful": True,
                "sql_validation_error": None,
                "correlation_id": valid_generation_result.get("correlation_id", ""),
            }
        elif invalid_generation_result:
            # SQL validation failed - provide detailed error info
            error_type = invalid_generation_result.get("type", "UNKNOWN")
            error_message = invalid_generation_result.get("error", "Unknown validation error")
            
            # Don't retry for certain error types
            non_retryable_errors = ["TIME_OUT", "SYNTAX_ERROR", "PERMISSION_DENIED"]
            is_non_retryable = error_type in non_retryable_errors
            
            return {
                "monitor_sql": invalid_generation_result.get("sql", monitor_sql),
                "explanation": result["explanation"],
                "sql_validation_successful": False,
                "sql_validation_error": f"SQL validation failed (type: {error_type}): {error_message}",
                "correlation_id": invalid_generation_result.get("correlation_id", ""),
                "is_non_retryable": is_non_retryable,
            }
        else:
            # No result from post processor - fall back to simple validation
            logger.warning("SQLGenPostProcessor returned no results, falling back to add_quotes validation")
            quoted_sql, error_message = add_quotes(monitor_sql)
            
            if error_message:
                return {
                    "monitor_sql": monitor_sql,
                    "explanation": result["explanation"],
                    "sql_validation_successful": False,
                    "sql_validation_error": f"SQL syntax error: {error_message}",
                    "is_non_retryable": True,  # Syntax errors should not be retried
                }
            else:
                return {
                    "monitor_sql": quoted_sql,
                    "explanation": result["explanation"],
                    "sql_validation_successful": True,
                    "sql_validation_error": None,
                }

    except Exception as e:
        logger.error(f"Error in monitor SQL post-processing: {str(e)}")
        return {
            "monitor_sql": "",
            "explanation": f"Error processing SQL generation: {str(e)}",
            "sql_validation_successful": False,
            "sql_validation_error": str(e),
            "is_non_retryable": True,  # General errors should not be retried
        }


@observe(capture_input=False)
async def sql_validation(
    sql_post_process: dict,
    sql_executor: SQLExecutor,
    project_id: Optional[str] = None,
) -> dict:
    # Update status to validating
    global _monitor_status_updater
    if _monitor_status_updater:
        _monitor_status_updater("validating")
    
    monitor_sql = sql_post_process.get("monitor_sql", "")
    sql_validation_successful = sql_post_process.get("sql_validation_successful", False)
    sql_validation_error = sql_post_process.get("sql_validation_error")

    # If SQL syntax validation already failed, return that error
    if not sql_validation_successful:
        return {
            "execution_successful": False,
            "execution_error": f"SQL syntax validation failed: {sql_validation_error}",
            "sample_result": None,
            "key_insights": [
                "The generated SQL has syntax errors and needs correction.",
                "SQL validation failed during the quoting and syntax check phase.",
                "Consider simplifying the query or checking for unsupported SQL features.",
            ],
        }

    if not monitor_sql:
        return {
            "execution_successful": False,
            "execution_error": "No monitor SQL to validate",
            "sample_result": None,
            "key_insights": [
                "No SQL was generated for monitoring.",
                "The monitor generation process may have failed.",
                "Check the input parameters and try again.",
            ],
        }

    try:
        # Execute the monitor SQL to validate it works
        result = await sql_executor.run(
            sql=monitor_sql,
            project_id=project_id,
            limit=1  # Just need to verify it executes and returns a value
        )

        execution_result = result.get("execute_sql", {}).get("results")

        if execution_result and execution_result.get("data"):
            # Handle both single-value and comparative SQL results
            data = execution_result.get("data", [])
            if len(data) > 0:
                row = data[0]

                # Modern templated monitoring query validation
                # Count numeric values for monitoring
                numeric_values = []
                for val in row:
                    try:
                        numeric_values.append(float(val))
                    except (ValueError, TypeError):
                        # Skip non-numeric values (identifiers, labels, etc.)
                        pass
                
                if len(numeric_values) >= 1:
                    # Valid templated query - has at least one numeric value
                    # Convert execution result to string representation for GraphQL String field
                    sample_result_str = str(execution_result) if execution_result else None
                    return {
                        "execution_successful": True,
                        "execution_error": None,
                        "sample_result": sample_result_str,  # Convert to string for GraphQL compatibility
                        "key_insights": [
                            "The templated monitor SQL executed successfully.",
                            f"Returned {len(row)} columns with {len(numeric_values)} numeric values for monitoring.",
                            "The query is ready for template-based monitoring and alerting.",
                        ],
                    }
                else:
                    # Convert execution result to string representation for GraphQL String field
                    sample_result_str = str(execution_result) if execution_result else None
                    return {
                        "execution_successful": False,
                        "execution_error": f"Query returned no numeric values: {row}",
                        "sample_result": sample_result_str,  # Convert to string for GraphQL compatibility
                        "key_insights": [
                            "The query executed but returned no numeric values for monitoring.",
                            "Template-based monitoring queries must return at least one numeric value.",
                            "Consider including aggregation functions like COUNT, SUM, AVG, etc.",
                        ],
                    }
        else:
            return {
                "execution_successful": False,
                "execution_error": "Query executed but returned no data",
                "sample_result": None,
                "key_insights": [
                    "The query executed successfully but returned no data.",
                    "This could indicate empty tables or overly restrictive WHERE conditions.",
                    "Consider adjusting the query to ensure it returns data for monitoring.",
                ],
            }

    except Exception as e:
        return {
            "execution_successful": False,
            "execution_error": str(e),
            "sample_result": None,
            "key_insights": [
                "The query failed to execute due to an error.",
                f"Error details: {str(e)}",
                "Check the SQL syntax, table names, and column references.",
            ],
        }


@observe(capture_input=False)
def ai_sql_analysis_prompt(
    sql_post_process: dict,
    prompt_builder: PromptBuilder,
) -> dict:
    monitor_sql = sql_post_process.get("monitor_sql", "")
    explanation = sql_post_process.get("explanation", "")

    user_prompt = f"""
You are an expert SQL analyst specializing in comparative monitoring queries. Analyze the provided monitor SQL and explanation to extract the comparison configuration.

**Monitor SQL:**
```sql
{monitor_sql}
```

**Explanation:**
{explanation}

**Your Task:**
Analyze the SQL structure and explanation to determine:

1. **Comparison Type**: What type of comparison is being performed?
   - "percentage_change": Calculates percentage difference between current and baseline
   - "absolute_difference": Calculates raw difference between current and baseline
   - "ratio": Calculates ratio between current and baseline values

2. **Baseline Period**: What baseline period is being used?
   - "previous_period": Compares to the immediately previous time period
   - "rolling_average": Compares to a rolling average over multiple periods
   - "year_over_year": Compares to the same period in the previous year

3. **Baseline Duration**: What is the duration of the baseline period?
   - Examples: "7 days", "30 days", "90 days", "1 year", "1 month", "1 quarter"

**Analysis Guidelines:**
- Look for SQL patterns like DATE_TRUNC, INTERVAL clauses, and time-based WHERE conditions
- Examine calculation logic (multiplication by 100 for percentages, division for ratios)
- Consider the business context from the explanation
- If the SQL uses year-based intervals (INTERVAL '1' YEAR), it's likely year_over_year
- If the SQL uses month-based intervals (INTERVAL '1' MONTH), it's likely previous_period
- If the SQL calculates percentage change (* 100), it's percentage_change
- If the SQL only calculates ratios (division without * 100), it's ratio

**Response Format:**
Return a JSON object with:
{{
    "comparison_type": "percentage_change|absolute_difference|ratio",
    "baseline_period": "previous_period|rolling_average|year_over_year",
    "baseline_duration": "duration string (e.g., '30 days', '1 year')",
    "confidence": "high|medium|low",
    "reasoning": "Brief explanation of how you determined these values"
}}
"""

    return prompt_builder.run(prompt=user_prompt)


@observe(capture_input=False)
def ai_sql_analysis_post_process(ai_sql_analysis: dict) -> dict:
    try:
        response_data = ai_sql_analysis["replies"]
        if not response_data:
            raise ValueError("No response from AI SQL analysis")

        # Parse the JSON response
        result = orjson.loads(response_data[0])

        required_fields = ["comparison_type", "baseline_period", "baseline_duration"]
        for field in required_fields:
            if field not in result:
                logger.warning(f"Missing field {field} in AI analysis, using default")
                if field == "comparison_type":
                    result[field] = "percentage_change"
                elif field == "baseline_period":
                    result[field] = "previous_period"
                elif field == "baseline_duration":
                    result[field] = "30 days"

        logger.info(f"AI SQL Analysis completed with confidence: {result.get('confidence', 'unknown')}")
        return result

    except Exception as e:
        logger.error(f"Error in AI SQL analysis post-processing: {str(e)}")
        # Return safe defaults
        return {
            "comparison_type": "percentage_change",
            "baseline_period": "previous_period",
            "baseline_duration": "30 days",
            "confidence": "low",
            "reasoning": f"Analysis failed, using defaults. Error: {str(e)}"
        }


@observe(capture_input=False)
def sql_regeneration_prompt(
    original_message: str,
    existing_sql: str,
    business_context: str,
    monitoring_approach: str,
    recommended_aggregation: str,
    comparison_type: str,
    baseline_period: str,
    baseline_duration: str,
    thread_data: Dict[str, Any],
    prompt_builder: PromptBuilder,
) -> dict:
    # Extract schema context from thread data
    schema_context = thread_data.get("schemaContext", "")

    user_prompt = f"""
You are an expert SQL developer specializing in creating configurable comparative monitoring queries. Generate a monitor SQL query based on the specified configuration.

**Context:**
- Original Question: {original_message}
- Business Context: {business_context}
- Monitoring Approach: {monitoring_approach}
- Recommended Aggregation: {recommended_aggregation}
- Existing SQL: {existing_sql}

**Database Schema Context:**
{schema_context if schema_context else "No schema context available - use existing SQL structure for reference."}

**Required Configuration:**
- Comparison Type: {comparison_type}
- Baseline Period: {baseline_period}
- Baseline Duration: {baseline_duration}

{TEXT_TO_SQL_RULES}

{calculated_field_instructions}

{metric_instructions}

**Your Task:**
Generate a comparative monitoring SQL query that implements the exact configuration specified above. Use the database schema context to ensure accurate column names, data types, and table references.

**Configuration Guidelines:**

1. **SQL Rules Compliance:**
   - STRICTLY FOLLOW the SQL RULES above for proper syntax and structure
   - ALWAYS QUALIFY column names with their table name or table alias to avoid ambiguity
   - Use proper time zone casting with TIMESTAMP WITH TIME ZONE for date comparisons
   - Use CROSS JOIN syntax instead of comma joins for CTE combinations
   - Always use table aliases when referencing CTE columns (e.g., cp.current_value not current_period.current_value)
   - Ensure all CTE column names are unique and properly aliased

2. **Schema Validation:**
   - Validate all column names and table references against the provided schema
   - Use correct data types for calculations based on schema information
   - Ensure all referenced tables and columns exist in the schema

3. **Comparison Type Implementation:**
   - "percentage_change": Include `((current_value - baseline_value) / NULLIF(baseline_value, 0) * 100) AS percentage_change`
   - "absolute_difference": Include `(current_value - baseline_value) AS absolute_difference`
   - "ratio": Include `(current_value / NULLIF(baseline_value, 0)) AS ratio`

2. **Baseline Period Implementation:**
   - "previous_period": Compare current period to immediately previous period of same duration
   - "rolling_average": Compare current period to rolling average over multiple periods
   - "year_over_year": Compare current period to same period in previous year using DATE_TRUNC('YEAR', ...)

3. **Baseline Duration Implementation:**
   - Use INTERVAL clauses matching the specified duration
   - For "30 days": INTERVAL '30' DAY
   - For "1 year": INTERVAL '1' YEAR
   - For "1 month": INTERVAL '1' MONTH

**Required SQL Structure:**
```sql
WITH current_period AS (
  SELECT {recommended_aggregation}(...) as current_value
  FROM ...
  WHERE [time conditions for current period based on baseline_duration]
),
baseline_period AS (
  SELECT {recommended_aggregation}(...) as baseline_value
  FROM ...
  WHERE [time conditions for baseline period based on baseline_period and baseline_duration]
)
SELECT
  cp.current_value,
  bp.baseline_value,
  [comparison calculation based on comparison_type]
FROM current_period cp
CROSS JOIN baseline_period bp
```

**Response Format:**
Return a JSON object with:
{{
    "monitor_sql": "WITH current_period AS (...) SELECT ...",
    "explanation": "Clear explanation of the implemented configuration and SQL logic"
}}
"""

    return prompt_builder.run(prompt=user_prompt)


@observe(capture_input=False)
def sql_regeneration_post_process(sql_regeneration: dict) -> dict:
    try:
        response_data = sql_regeneration["replies"]
        if not response_data:
            raise ValueError("No response from SQL regeneration")

        # Parse the JSON response
        result = orjson.loads(response_data[0])

        required_fields = ["monitor_sql", "explanation"]
        for field in required_fields:
            if field not in result:
                raise ValueError(f"Missing required field: {field}")

        return result

    except Exception as e:
        logger.error(f"Error in SQL regeneration post-processing: {str(e)}")
        raise ValueError(f"Failed to process SQL regeneration response: {str(e)}")


@observe(capture_input=False)
def post_process(
    reasoning_post_process: dict,
    sql_post_process: dict,
    ai_sql_analysis_post_process: dict,
    sql_validation: dict,
) -> dict:
    # Use AI analysis results with fallback to original values
    comparison_type = ai_sql_analysis_post_process.get("comparison_type") or sql_post_process.get("comparison_type") or "percentage_change"
    baseline_period = ai_sql_analysis_post_process.get("baseline_period") or sql_post_process.get("baseline_period") or "previous_period"
    baseline_duration = ai_sql_analysis_post_process.get("baseline_duration") or sql_post_process.get("baseline_duration") or "30 days"

    return {
        "monitor_sql": sql_post_process.get("monitor_sql", ""),
        "reasoning_steps": [
            f"Business Analysis: {reasoning_post_process.get('business_context', '')}",
            f"Monitoring Approach: {reasoning_post_process.get('monitoring_approach', '')}",
            f"SQL Explanation: {sql_post_process.get('explanation', '')}",
            f"AI Analysis: {ai_sql_analysis_post_process.get('reasoning', 'Analysis completed')} (Confidence: {ai_sql_analysis_post_process.get('confidence', 'unknown')})",
            f"Comparison Type: {comparison_type}",
            f"Baseline Period: {baseline_period} ({baseline_duration})",
        ],
        "validation_results": {
            "execution_successful": sql_validation.get("execution_successful", False),
            "execution_error": sql_validation.get("execution_error"),
            "sample_result": sql_validation.get("sample_result"),
            "key_insights": sql_validation.get("key_insights", reasoning_post_process.get("key_insights", [])),
        },
    }


## End of Pipeline


class MonitorReasoningResult(BaseModel):
    business_context: str
    monitoring_approach: str
    key_insights: list[str]
    recommended_aggregation: str


class MonitorSQLResult(BaseModel):
    monitor_sql: str
    explanation: str


class AIAnalysisResult(BaseModel):
    comparison_type: str
    baseline_period: str
    baseline_duration: str
    confidence: str
    reasoning: str


class SQLRegenerationResult(BaseModel):
    monitor_sql: str
    explanation: str


class MonitorGenerationResult(BaseModel):
    monitor_sql: str
    reasoning_steps: list[str]
    template_suggestions: list[dict]
    validation_results: dict


class MonitorRegenerationResult(BaseModel):
    monitor_sql: str
    explanation: str
    validation_results: dict


MONITOR_REASONING_MODEL_KWARGS = {
    "response_format": {
        "type": "json_schema",
        "json_schema": {
            "name": "monitor_reasoning_result",
            "schema": MonitorReasoningResult.model_json_schema(),
        },
    }
}

MONITOR_SQL_MODEL_KWARGS = {
    "response_format": {
        "type": "json_schema",
        "json_schema": {
            "name": "monitor_sql_result",
            "schema": MonitorSQLResult.model_json_schema(),
        },
    }
}

AI_ANALYSIS_MODEL_KWARGS = {
    "response_format": {
        "type": "json_schema",
        "json_schema": {
            "name": "ai_analysis_result",
            "schema": AIAnalysisResult.model_json_schema(),
        },
    }
}

SQL_REGENERATION_MODEL_KWARGS = {
    "response_format": {
        "type": "json_schema",
        "json_schema": {
            "name": "sql_regeneration_result",
            "schema": SQLRegenerationResult.model_json_schema(),
        },
    }
}


class MonitorGeneration(BasicPipeline):
    def __init__(
        self,
        llm_provider: LLMProvider,
        engine: Engine,
        sql_executor: SQLExecutor,
        embedder: Any = None,
        document_store: Any = None,
        engine_timeout: Optional[float] = 30.0,
        **kwargs,
    ):
        self._components = {
            "generator": llm_provider.get_generator(
                system_prompt="""You are an expert data monitoring specialist with deep knowledge of SQL, business intelligence, and data alerting systems.

Your role is to analyze user questions and response data to understand business intent, then create effective monitoring SQL queries.

Focus on:
1. Understanding the business context from comprehensive response data
2. Creating clear reasoning for monitoring approaches
3. Generating precise SQL queries that return single numeric values
4. Providing realistic threshold suggestions based on business context

Always return valid JSON responses with all required fields.""",
                generation_kwargs=MONITOR_REASONING_MODEL_KWARGS,
            ),
            "sql_generator": llm_provider.get_generator(
                system_prompt="""You are an expert SQL developer specializing in monitoring queries.

Your role is to transform business requirements into precise SQL monitoring queries.

Focus on:
1. Creating SQL that returns exactly one numeric value
2. Using appropriate aggregation functions
3. Ensuring queries can be run periodically
4. Providing clear explanations and threshold suggestions

Always return valid JSON responses with all required fields.""",
                generation_kwargs=MONITOR_SQL_MODEL_KWARGS,
            ),
            "ai_sql_analyzer": llm_provider.get_generator(
                system_prompt="""You are an expert SQL analyst specializing in comparative monitoring queries.

Your role is to analyze SQL queries and extract configuration parameters with high accuracy.

Focus on:
1. Understanding SQL structure and logic patterns
2. Identifying comparison types (percentage_change, absolute_difference, ratio)
3. Determining baseline periods (previous_period, rolling_average, year_over_year)
4. Extracting baseline durations from time intervals

Always return valid JSON responses with confidence scores.""",
                generation_kwargs=AI_ANALYSIS_MODEL_KWARGS,
            ),
            "sql_regenerator": llm_provider.get_generator(
                system_prompt="""You are an expert SQL developer specializing in configurable comparative monitoring queries.

Your role is to generate SQL queries based on specific user configurations.

Focus on:
1. Implementing exact user-specified comparison types
2. Creating proper baseline period logic
3. Using correct time intervals and durations
4. Maintaining comparative SQL structure with CTEs

Always return valid JSON responses with working SQL.""",
                generation_kwargs=SQL_REGENERATION_MODEL_KWARGS,
            ),
            "generator_name": llm_provider.get_model(),
            "prompt_builder": PromptBuilder(template="{{ prompt }}"),
            "post_processor": SQLGenPostProcessor(engine=engine),
            "sql_executor": sql_executor,
            "embedder": embedder,
            "document_store": document_store,
        }

        self._configs = {
            "engine_timeout": engine_timeout,
        }

        super().__init__(
            AsyncDriver({}, sys.modules[__name__], result_builder=base.DictResult())
        )

    def set_status_updater(self, updater_func):
        """Set the status updater function for the pipeline"""
        global _monitor_status_updater
        _monitor_status_updater = updater_func
    
    def clear_status_updater(self):
        """Clear the status updater function"""
        global _monitor_status_updater
        _monitor_status_updater = None

    @observe(name="Monitor Generation")
    async def run(
        self,
        original_message: str,
        sql_reasoning: str,
        existing_sql: str,
        thread_data: Dict[str, Any],
        clarification_prompt: str = "",
        project_id: Optional[str] = None,
        query_id: Optional[str] = None,
        trace_id: Optional[str] = None,
    ) -> dict:
        logger.info("Monitor Generation pipeline is running...")
        
        # First, retrieve schema information if embedder and document_store are available
        schema_context = ""
        if self._components.get("embedder") and self._components.get("document_store"):
            try:
                schema_result = await schema_retrieval_for_monitor(
                    original_message=original_message,
                    existing_sql=existing_sql,
                    project_id=project_id,
                    embedder=self._components["embedder"],
                    document_store=self._components["document_store"],
                )
                schema_context = schema_result.get("schema_context", "")
                logger.info(f"Schema retrieval completed. Schema context length: {len(schema_context)}")
            except Exception as e:
                logger.warning(f"Schema retrieval failed: {e}")
        
        # Merge schema context with existing thread_data
        enhanced_thread_data = dict(thread_data)
        if schema_context:
            enhanced_thread_data["schemaContext"] = schema_context
        elif "schemaContext" not in enhanced_thread_data:
            enhanced_thread_data["schemaContext"] = ""
        
        return await self._pipe.execute(
            ["post_process"],
            inputs={
                "original_message": original_message,
                "sql_reasoning": sql_reasoning,
                "existing_sql": existing_sql,
                "thread_data": enhanced_thread_data,
                "clarification_prompt": clarification_prompt,
                "project_id": project_id,
                "query_id": query_id,
                **self._components,
                **self._configs,
            },
        )


    async def chat_refinement(
        self,
        original_message: str,
        existing_sql: str,
        chat_prompt: str,
        thread_data: Dict[str, Any],
        project_id: Optional[str] = None,
        query_id: Optional[str] = None,
        trace_id: Optional[str] = None,
    ) -> dict:
        """
        Refine monitor SQL based on chat conversation.
        
        Args:
            original_message: The original user question
            existing_sql: The current monitor SQL
            chat_prompt: User's refinement request
            thread_data: Thread context data including previous generation results
            project_id: Project identifier
            query_id: Query identifier
            trace_id: Trace identifier for logging
            
        Returns:
            dict: Contains refined monitor_sql, explanation, and validation results
        """
        logger.info(f"Refining monitor SQL with chat: {chat_prompt}")
        
        # Update status to understanding (chat refinement phase)
        global _monitor_status_updater
        if _monitor_status_updater:
            _monitor_status_updater("understanding")
        
        # Retrieve schema information for refinement
        schema_context = ""
        if self._components.get("embedder") and self._components.get("document_store"):
            try:
                schema_result = await schema_retrieval_for_monitor(
                    original_message=original_message,
                    existing_sql=existing_sql,
                    project_id=project_id,
                    embedder=self._components["embedder"],
                    document_store=self._components["document_store"],
                )
                schema_context = schema_result.get("schema_context", "")
                logger.info(f"Schema retrieval for chat refinement completed. Schema context length: {len(schema_context)}")
            except Exception as e:
                logger.warning(f"Schema retrieval failed during chat refinement: {e}")
        
        # Build comprehensive refinement prompt
        refinement_prompt = f"""
        CHAT REFINEMENT REQUEST:
        {chat_prompt}
        
        CONTEXT:
        - Original Question: {original_message}
        - Current Monitor SQL: {existing_sql}
        - Database Schema: {schema_context}
        
        PREVIOUS GENERATION RESULTS:
        {thread_data.get('answerDetail', {}).get('content', '')}
        
        INSTRUCTIONS:
        Based on the chat request above, modify the current monitor SQL to incorporate the user's requested changes.
        Keep the core monitoring logic intact while applying the specific refinements requested.
        Ensure the refined SQL maintains the same output structure and monitoring effectiveness.
        """
        
        # Run the full pipeline with refinement context
        if _monitor_status_updater:
            _monitor_status_updater("reasoning")
        
        enhanced_thread_data = dict(thread_data)
        if schema_context:
            enhanced_thread_data["schemaContext"] = schema_context
        elif "schemaContext" not in enhanced_thread_data:
            enhanced_thread_data["schemaContext"] = ""
        
        result = await self._pipe.execute(
            ["post_process"],
            inputs={
                "original_message": original_message,
                "sql_reasoning": refinement_prompt,
                "existing_sql": existing_sql,
                "thread_data": enhanced_thread_data,
                "clarification_prompt": chat_prompt,
                "project_id": project_id,
                "query_id": query_id,
                **self._components,
                **self._configs,
            },
        )
        
        # Format the response for refined SQL
        return result.get("post_process", {})


if __name__ == "__main__":
    from src.pipelines.common import dry_run_pipeline

    dry_run_pipeline(
        MonitorGeneration,
        "monitor_generation",
        original_message="How many orders were placed last month?",
        sql_reasoning="User wants to count orders from the previous month...",
        existing_sql="SELECT COUNT(*) FROM orders WHERE created_at >= '2024-01-01'",
        thread_data={
            "answerDetail": {"content": "There were 1,234 orders placed last month."},
            "chartDetail": {"chartType": "bar", "description": "Monthly order counts"},
            "breakdownDetail": {"steps": [{"summary": "Count orders", "sql": "SELECT COUNT(*) FROM orders"}]}
        },
        clarification_prompt="",
    )
