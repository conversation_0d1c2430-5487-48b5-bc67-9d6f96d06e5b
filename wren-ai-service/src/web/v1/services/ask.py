import asyncio
import logging
from typing import Dict, List, Literal, Optional

from cachetools import TTL<PERSON>ache
from langfuse.decorators import observe
from pydantic import AliasChoices, BaseModel, Field

from src.core.pipeline import BasicPipeline
from src.utils import trace_metadata
from src.web.v1.services import BaseRequest, SSEEvent

logger = logging.getLogger("wren-ai-service")


class AskHistory(BaseModel):
    sql: str
    question: str


# POST /v1/asks
class AskRequest(BaseRequest):
    query: str
    # don't recommend to use id as a field name, but it's used in the older version of API spec
    # so we need to support as a choice, and will remove it in the future
    mdl_hash: Optional[str] = Field(validation_alias=AliasChoices("mdl_hash", "id"))
    histories: Optional[list[AskHistory]] = Field(default_factory=list)
    ignore_sql_generation_reasoning: bool = False
    enable_column_pruning: bool = False
    use_dry_plan: bool = False
    allow_dry_plan_fallback: bool = True


class AskResponse(BaseModel):
    query_id: str


# PATCH /v1/asks/{query_id}
class StopAskRequest(BaseRequest):
    status: Literal["stopped"]


class StopAskResponse(BaseModel):
    query_id: str


# GET /v1/asks/{query_id}/result
class AskResult(BaseModel):
    sql: str
    type: Literal["llm", "view"] = "llm"
    viewId: Optional[str] = None


class AskError(BaseModel):
    code: Literal["NO_RELEVANT_DATA", "NO_RELEVANT_SQL", "OTHERS"]
    message: str


class AskResultRequest(BaseModel):
    query_id: str


class _AskResultResponse(BaseModel):
    status: Literal[
        "understanding",
        "searching",
        "planning",
        "generating",
        "correcting",
        "finished",
        "failed",
        "stopped",
    ]
    rephrased_question: Optional[str] = None
    intent_reasoning: Optional[str] = None
    sql_generation_reasoning: Optional[str] = None
    type: Optional[Literal["GENERAL", "TEXT_TO_SQL"]] = None
    retrieved_tables: Optional[List[str]] = None
    response: Optional[List[AskResult]] = None
    invalid_sql: Optional[str] = None
    error: Optional[AskError] = None
    trace_id: Optional[str] = None
    is_followup: bool = False
    general_type: Optional[
        Literal["MISLEADING_QUERY", "DATA_ASSISTANCE", "USER_GUIDE"]
    ] = None


class AskResultResponse(_AskResultResponse):
    is_followup: Optional[bool] = Field(False, exclude=True)
    general_type: Optional[
        Literal["MISLEADING_QUERY", "DATA_ASSISTANCE", "USER_GUIDE"]
    ] = Field(None, exclude=True)


# POST /v1/ask-feedbacks
class AskFeedbackRequest(BaseRequest):
    question: str
    tables: List[str]
    sql_generation_reasoning: str
    sql: str


class AskFeedbackResponse(BaseModel):
    query_id: str


# PATCH /v1/ask-feedbacks/{query_id}
class StopAskFeedbackRequest(BaseRequest):
    status: Literal["stopped"]


class StopAskFeedbackResponse(BaseModel):
    query_id: str


# GET /v1/ask-feedbacks/{query_id}
class AskFeedbackResultRequest(BaseModel):
    query_id: str


class AskFeedbackResultResponse(BaseModel):
    status: Literal[
        "searching",
        "generating",
        "correcting",
        "finished",
        "failed",
        "stopped",
    ]
    invalid_sql: Optional[str] = None
    error: Optional[AskError] = None
    response: Optional[List[AskResult]] = None
    trace_id: Optional[str] = None


# Monitor Generation Models
class GenerateMonitorRequest(BaseRequest):
    original_message: str
    sql_reasoning: str
    existing_sql: str
    thread_data: dict
    clarification_prompt: Optional[str] = ""


class GenerateMonitorResponse(BaseModel):
    query_id: str


class MonitorGenerationResult(BaseModel):
    monitor_sql: str
    reasoning_steps: list[str]
    template_suggestions: list[dict]
    validation_results: dict  # Contains execution_successful, execution_error, sample_result, key_insights


class MonitorGenerationResultResponse(BaseModel):
    status: Literal[
        "understanding",
        "reasoning",
        "generating",
        "validating",
        "finished",
        "failed",
        "stopped",
    ]
    error: Optional[AskError] = None
    response: Optional[MonitorGenerationResult] = None
    trace_id: Optional[str] = None






# Chat with Monitor Models
class ChatWithMonitorRequest(BaseRequest):
    original_message: str
    existing_sql: str
    chat_prompt: str
    thread_data: dict


class AskService:
    def __init__(
        self,
        pipelines: Dict[str, BasicPipeline],
        allow_intent_classification: bool = True,
        allow_sql_generation_reasoning: bool = True,
        allow_sql_functions_retrieval: bool = True,
        enable_column_pruning: bool = False,
        max_sql_correction_retries: int = 3,
        max_histories: int = 5,
        maxsize: int = 1_000_000,
        ttl: int = 120,
    ):
        self._pipelines = pipelines
        self._ask_results: Dict[str, AskResultResponse] = TTLCache(
            maxsize=maxsize, ttl=ttl
        )
        self._ask_feedback_results: Dict[str, AskFeedbackResultResponse] = TTLCache(
            maxsize=maxsize, ttl=ttl
        )
        self._monitor_generation_results: Dict[str, MonitorGenerationResultResponse] = TTLCache(
            maxsize=maxsize, ttl=ttl
        )
        self._allow_sql_generation_reasoning = allow_sql_generation_reasoning
        self._allow_sql_functions_retrieval = allow_sql_functions_retrieval
        self._allow_intent_classification = allow_intent_classification
        self._enable_column_pruning = enable_column_pruning
        self._max_histories = max_histories
        self._max_sql_correction_retries = max_sql_correction_retries

    def _is_stopped(self, query_id: str, container: dict):
        if (
            result := container.get(query_id)
        ) is not None and result.status == "stopped":
            return True

        return False

    @observe(name="Ask Question")
    @trace_metadata
    async def ask(
        self,
        ask_request: AskRequest,
        **kwargs,
    ):
        trace_id = kwargs.get("trace_id")
        results = {
            "ask_result": {},
            "metadata": {
                "type": "",
                "error_type": "",
                "error_message": "",
                "request_from": ask_request.request_from,
            },
        }

        query_id = ask_request.query_id
        histories = ask_request.histories[: self._max_histories][
            ::-1
        ]  # reverse the order of histories
        rephrased_question = None
        intent_reasoning = None
        sql_generation_reasoning = None
        sql_samples = []
        instructions = []
        api_results = []
        table_names = []
        error_message = None
        invalid_sql = None
        allow_sql_generation_reasoning = (
            self._allow_sql_generation_reasoning
            and not ask_request.ignore_sql_generation_reasoning
        )
        enable_column_pruning = (
            self._enable_column_pruning or ask_request.enable_column_pruning
        )
        allow_sql_functions_retrieval = self._allow_sql_functions_retrieval
        max_sql_correction_retries = self._max_sql_correction_retries
        current_sql_correction_retries = 0
        use_dry_plan = ask_request.use_dry_plan
        allow_dry_plan_fallback = ask_request.allow_dry_plan_fallback

        try:
            user_query = ask_request.query

            # ask status can be understanding, searching, generating, finished, failed, stopped
            # we will need to handle business logic for each status
            if not self._is_stopped(query_id, self._ask_results):
                self._ask_results[query_id] = AskResultResponse(
                    status="understanding",
                    trace_id=trace_id,
                    is_followup=True if histories else False,
                )

                historical_question = await self._pipelines["historical_question"].run(
                    query=user_query,
                    project_id=ask_request.project_id,
                )

                # we only return top 1 result
                historical_question_result = historical_question.get(
                    "formatted_output", {}
                ).get("documents", [])[:1]

                if historical_question_result:
                    api_results = [
                        AskResult(
                            **{
                                "sql": result.get("statement"),
                                "type": "view" if result.get("viewId") else "llm",
                                "viewId": result.get("viewId"),
                            }
                        )
                        for result in historical_question_result
                    ]
                    sql_generation_reasoning = ""
                else:
                    # Run both pipeline operations concurrently
                    sql_samples_task, instructions_task = await asyncio.gather(
                        self._pipelines["sql_pairs_retrieval"].run(
                            query=user_query,
                            project_id=ask_request.project_id,
                        ),
                        self._pipelines["instructions_retrieval"].run(
                            query=user_query,
                            project_id=ask_request.project_id,
                        ),
                    )

                    # Extract results from completed tasks
                    sql_samples = sql_samples_task["formatted_output"].get(
                        "documents", []
                    )
                    instructions = instructions_task["formatted_output"].get(
                        "documents", []
                    )

                    if self._allow_intent_classification:
                        intent_classification_result = (
                            await self._pipelines["intent_classification"].run(
                                query=user_query,
                                histories=histories,
                                sql_samples=sql_samples,
                                instructions=instructions,
                                project_id=ask_request.project_id,
                                configuration=ask_request.configurations,
                            )
                        ).get("post_process", {})
                        intent = intent_classification_result.get("intent")
                        rephrased_question = intent_classification_result.get(
                            "rephrased_question"
                        )
                        intent_reasoning = intent_classification_result.get("reasoning")

                        if rephrased_question:
                            user_query = rephrased_question

                        if intent == "MISLEADING_QUERY":
                            asyncio.create_task(
                                self._pipelines["misleading_assistance"].run(
                                    query=user_query,
                                    histories=histories,
                                    db_schemas=intent_classification_result.get(
                                        "db_schemas"
                                    ),
                                    language=ask_request.configurations.language,
                                    query_id=ask_request.query_id,
                                )
                            )

                            self._ask_results[query_id] = AskResultResponse(
                                status="finished",
                                type="GENERAL",
                                rephrased_question=rephrased_question,
                                intent_reasoning=intent_reasoning,
                                trace_id=trace_id,
                                is_followup=True if histories else False,
                                general_type="MISLEADING_QUERY",
                            )
                            results["metadata"]["type"] = "MISLEADING_QUERY"
                            return results
                        elif intent == "GENERAL":
                            asyncio.create_task(
                                self._pipelines["data_assistance"].run(
                                    query=user_query,
                                    histories=histories,
                                    db_schemas=intent_classification_result.get(
                                        "db_schemas"
                                    ),
                                    language=ask_request.configurations.language,
                                    query_id=ask_request.query_id,
                                )
                            )

                            self._ask_results[query_id] = AskResultResponse(
                                status="finished",
                                type="GENERAL",
                                rephrased_question=rephrased_question,
                                intent_reasoning=intent_reasoning,
                                trace_id=trace_id,
                                is_followup=True if histories else False,
                                general_type="DATA_ASSISTANCE",
                            )
                            results["metadata"]["type"] = "GENERAL"
                            return results
                        elif intent == "USER_GUIDE":
                            asyncio.create_task(
                                self._pipelines["user_guide_assistance"].run(
                                    query=user_query,
                                    language=ask_request.configurations.language,
                                    query_id=ask_request.query_id,
                                )
                            )

                            self._ask_results[query_id] = AskResultResponse(
                                status="finished",
                                type="GENERAL",
                                rephrased_question=rephrased_question,
                                intent_reasoning=intent_reasoning,
                                trace_id=trace_id,
                                is_followup=True if histories else False,
                                general_type="USER_GUIDE",
                            )
                            results["metadata"]["type"] = "GENERAL"
                            return results
                        else:
                            self._ask_results[query_id] = AskResultResponse(
                                status="understanding",
                                type="TEXT_TO_SQL",
                                rephrased_question=rephrased_question,
                                intent_reasoning=intent_reasoning,
                                trace_id=trace_id,
                                is_followup=True if histories else False,
                            )
            if not self._is_stopped(query_id, self._ask_results) and not api_results:
                self._ask_results[query_id] = AskResultResponse(
                    status="searching",
                    type="TEXT_TO_SQL",
                    rephrased_question=rephrased_question,
                    intent_reasoning=intent_reasoning,
                    trace_id=trace_id,
                    is_followup=True if histories else False,
                )

                retrieval_result = await self._pipelines["db_schema_retrieval"].run(
                    query=user_query,
                    histories=histories,
                    project_id=ask_request.project_id,
                    enable_column_pruning=enable_column_pruning,
                )
                _retrieval_result = retrieval_result.get(
                    "construct_retrieval_results", {}
                )
                documents = _retrieval_result.get("retrieval_results", [])
                table_names = [document.get("table_name") for document in documents]
                table_ddls = [document.get("table_ddl") for document in documents]

                if not documents:
                    logger.exception(f"ask pipeline - NO_RELEVANT_DATA: {user_query}")
                    if not self._is_stopped(query_id, self._ask_results):
                        self._ask_results[query_id] = AskResultResponse(
                            status="failed",
                            type="TEXT_TO_SQL",
                            error=AskError(
                                code="NO_RELEVANT_DATA",
                                message="No relevant data",
                            ),
                            rephrased_question=rephrased_question,
                            intent_reasoning=intent_reasoning,
                            trace_id=trace_id,
                            is_followup=True if histories else False,
                        )
                    results["metadata"]["error_type"] = "NO_RELEVANT_DATA"
                    results["metadata"]["type"] = "TEXT_TO_SQL"
                    return results

            if (
                not self._is_stopped(query_id, self._ask_results)
                and not api_results
                and allow_sql_generation_reasoning
            ):
                self._ask_results[query_id] = AskResultResponse(
                    status="planning",
                    type="TEXT_TO_SQL",
                    rephrased_question=rephrased_question,
                    intent_reasoning=intent_reasoning,
                    retrieved_tables=table_names,
                    trace_id=trace_id,
                    is_followup=True if histories else False,
                )

                if histories:
                    sql_generation_reasoning = (
                        await self._pipelines["followup_sql_generation_reasoning"].run(
                            query=user_query,
                            contexts=table_ddls,
                            histories=histories,
                            sql_samples=sql_samples,
                            instructions=instructions,
                            configuration=ask_request.configurations,
                            query_id=query_id,
                        )
                    ).get("post_process", {})
                else:
                    sql_generation_reasoning = (
                        await self._pipelines["sql_generation_reasoning"].run(
                            query=user_query,
                            contexts=table_ddls,
                            sql_samples=sql_samples,
                            instructions=instructions,
                            configuration=ask_request.configurations,
                            query_id=query_id,
                        )
                    ).get("post_process", {})

                self._ask_results[query_id] = AskResultResponse(
                    status="planning",
                    type="TEXT_TO_SQL",
                    rephrased_question=rephrased_question,
                    intent_reasoning=intent_reasoning,
                    retrieved_tables=table_names,
                    sql_generation_reasoning=sql_generation_reasoning,
                    trace_id=trace_id,
                    is_followup=True if histories else False,
                )

            if not self._is_stopped(query_id, self._ask_results) and not api_results:
                self._ask_results[query_id] = AskResultResponse(
                    status="generating",
                    type="TEXT_TO_SQL",
                    rephrased_question=rephrased_question,
                    intent_reasoning=intent_reasoning,
                    retrieved_tables=table_names,
                    sql_generation_reasoning=sql_generation_reasoning,
                    trace_id=trace_id,
                    is_followup=True if histories else False,
                )

                if allow_sql_functions_retrieval:
                    sql_functions = await self._pipelines[
                        "sql_functions_retrieval"
                    ].run(
                        project_id=ask_request.project_id,
                    )
                else:
                    sql_functions = []

                has_calculated_field = _retrieval_result.get(
                    "has_calculated_field", False
                )
                has_metric = _retrieval_result.get("has_metric", False)

                if histories:
                    text_to_sql_generation_results = await self._pipelines[
                        "followup_sql_generation"
                    ].run(
                        query=user_query,
                        contexts=table_ddls,
                        sql_generation_reasoning=sql_generation_reasoning,
                        histories=histories,
                        project_id=ask_request.project_id,
                        configuration=ask_request.configurations,
                        sql_samples=sql_samples,
                        instructions=instructions,
                        has_calculated_field=has_calculated_field,
                        has_metric=has_metric,
                        sql_functions=sql_functions,
                        use_dry_plan=use_dry_plan,
                        allow_dry_plan_fallback=allow_dry_plan_fallback,
                    )
                else:
                    text_to_sql_generation_results = await self._pipelines[
                        "sql_generation"
                    ].run(
                        query=user_query,
                        contexts=table_ddls,
                        sql_generation_reasoning=sql_generation_reasoning,
                        project_id=ask_request.project_id,
                        configuration=ask_request.configurations,
                        sql_samples=sql_samples,
                        instructions=instructions,
                        has_calculated_field=has_calculated_field,
                        has_metric=has_metric,
                        sql_functions=sql_functions,
                        use_dry_plan=use_dry_plan,
                        allow_dry_plan_fallback=allow_dry_plan_fallback,
                    )

                if sql_valid_result := text_to_sql_generation_results["post_process"][
                    "valid_generation_result"
                ]:
                    api_results = [
                        AskResult(
                            **{
                                "sql": sql_valid_result.get("sql"),
                                "type": "llm",
                            }
                        )
                    ]
                elif failed_dry_run_result := text_to_sql_generation_results[
                    "post_process"
                ]["invalid_generation_result"]:
                    while current_sql_correction_retries < max_sql_correction_retries:
                        invalid_sql = failed_dry_run_result["sql"]
                        error_message = failed_dry_run_result["error"]

                        if failed_dry_run_result["type"] == "TIME_OUT":
                            break

                        current_sql_correction_retries += 1

                        self._ask_results[query_id] = AskResultResponse(
                            status="correcting",
                            type="TEXT_TO_SQL",
                            rephrased_question=rephrased_question,
                            intent_reasoning=intent_reasoning,
                            retrieved_tables=table_names,
                            sql_generation_reasoning=sql_generation_reasoning,
                            trace_id=trace_id,
                            is_followup=True if histories else False,
                        )
                        sql_correction_results = await self._pipelines[
                            "sql_correction"
                        ].run(
                            contexts=table_ddls,
                            invalid_generation_result=failed_dry_run_result,
                            project_id=ask_request.project_id,
                            use_dry_plan=use_dry_plan,
                            allow_dry_plan_fallback=allow_dry_plan_fallback,
                        )

                        if valid_generation_result := sql_correction_results[
                            "post_process"
                        ]["valid_generation_result"]:
                            api_results = [
                                AskResult(
                                    **{
                                        "sql": valid_generation_result.get("sql"),
                                        "type": "llm",
                                    }
                                )
                            ]
                            break

                        failed_dry_run_result = sql_correction_results["post_process"][
                            "invalid_generation_result"
                        ]

            if api_results:
                if not self._is_stopped(query_id, self._ask_results):
                    self._ask_results[query_id] = AskResultResponse(
                        status="finished",
                        type="TEXT_TO_SQL",
                        response=api_results,
                        rephrased_question=rephrased_question,
                        intent_reasoning=intent_reasoning,
                        retrieved_tables=table_names,
                        sql_generation_reasoning=sql_generation_reasoning,
                        trace_id=trace_id,
                        is_followup=True if histories else False,
                    )
                results["ask_result"] = api_results
                results["metadata"]["type"] = "TEXT_TO_SQL"
            else:
                logger.exception(f"ask pipeline - NO_RELEVANT_SQL: {user_query}")
                if not self._is_stopped(query_id, self._ask_results):
                    self._ask_results[query_id] = AskResultResponse(
                        status="failed",
                        type="TEXT_TO_SQL",
                        error=AskError(
                            code="NO_RELEVANT_SQL",
                            message=error_message or "No relevant SQL",
                        ),
                        rephrased_question=rephrased_question,
                        intent_reasoning=intent_reasoning,
                        retrieved_tables=table_names,
                        sql_generation_reasoning=sql_generation_reasoning,
                        invalid_sql=invalid_sql,
                        trace_id=trace_id,
                        is_followup=True if histories else False,
                    )
                results["metadata"]["error_type"] = "NO_RELEVANT_SQL"
                results["metadata"]["error_message"] = error_message
                results["metadata"]["type"] = "TEXT_TO_SQL"

            return results
        except Exception as e:
            logger.exception(f"ask pipeline - OTHERS: {e}")

            self._ask_results[query_id] = AskResultResponse(
                status="failed",
                type="TEXT_TO_SQL",
                error=AskError(
                    code="OTHERS",
                    message=str(e),
                ),
                trace_id=trace_id,
                is_followup=True if histories else False,
            )

            results["metadata"]["error_type"] = "OTHERS"
            results["metadata"]["error_message"] = str(e)
            results["metadata"]["type"] = "TEXT_TO_SQL"
            return results

    def stop_ask(
        self,
        stop_ask_request: StopAskRequest,
    ):
        self._ask_results[stop_ask_request.query_id] = AskResultResponse(
            status="stopped",
        )

    def get_ask_result(
        self,
        ask_result_request: AskResultRequest,
    ) -> AskResultResponse:
        if (result := self._ask_results.get(ask_result_request.query_id)) is None:
            logger.exception(
                f"ask pipeline - OTHERS: {ask_result_request.query_id} is not found"
            )
            return AskResultResponse(
                status="failed",
                type="TEXT_TO_SQL",
                error=AskError(
                    code="OTHERS",
                    message=f"{ask_result_request.query_id} is not found",
                ),
            )

        return result

    async def get_ask_streaming_result(
        self,
        query_id: str,
    ):
        if self._ask_results.get(query_id):
            _pipeline_name = ""
            if self._ask_results.get(query_id).type == "GENERAL":
                if self._ask_results.get(query_id).general_type == "USER_GUIDE":
                    _pipeline_name = "user_guide_assistance"
                elif self._ask_results.get(query_id).general_type == "DATA_ASSISTANCE":
                    _pipeline_name = "data_assistance"
                elif self._ask_results.get(query_id).general_type == "MISLEADING_QUERY":
                    _pipeline_name = "misleading_assistance"
            elif self._ask_results.get(query_id).status == "planning":
                if self._ask_results.get(query_id).is_followup:
                    _pipeline_name = "followup_sql_generation_reasoning"
                else:
                    _pipeline_name = "sql_generation_reasoning"

            if _pipeline_name:
                async for chunk in self._pipelines[
                    _pipeline_name
                ].get_streaming_results(query_id):
                    event = SSEEvent(
                        data=SSEEvent.SSEEventMessage(message=chunk),
                    )
                    yield event.serialize()

    @observe(name="Ask Feedback")
    @trace_metadata
    async def ask_feedback(
        self,
        ask_feedback_request: AskFeedbackRequest,
        **kwargs,
    ):
        trace_id = kwargs.get("trace_id")
        results = {
            "ask_feedback_result": {},
            "metadata": {
                "error_type": "",
                "error_message": "",
                "request_from": ask_feedback_request.request_from,
            },
        }

        query_id = ask_feedback_request.query_id
        api_results = []
        error_message = None
        invalid_sql = None

        try:
            if not self._is_stopped(query_id, self._ask_feedback_results):
                self._ask_feedback_results[query_id] = AskFeedbackResultResponse(
                    status="searching",
                    trace_id=trace_id,
                )

                (
                    retrieval_task,
                    sql_samples_task,
                    instructions_task,
                ) = await asyncio.gather(
                    self._pipelines["db_schema_retrieval"].run(
                        tables=ask_feedback_request.tables,
                        project_id=ask_feedback_request.project_id,
                    ),
                    self._pipelines["sql_pairs_retrieval"].run(
                        query=ask_feedback_request.question,
                        project_id=ask_feedback_request.project_id,
                    ),
                    self._pipelines["instructions_retrieval"].run(
                        query=ask_feedback_request.question,
                        project_id=ask_feedback_request.project_id,
                    ),
                )

                if self._allow_sql_functions_retrieval:
                    sql_functions = await self._pipelines[
                        "sql_functions_retrieval"
                    ].run(
                        project_id=ask_feedback_request.project_id,
                    )
                else:
                    sql_functions = []

                # Extract results from completed tasks
                _retrieval_result = retrieval_task.get(
                    "construct_retrieval_results", {}
                )
                has_calculated_field = _retrieval_result.get(
                    "has_calculated_field", False
                )
                has_metric = _retrieval_result.get("has_metric", False)
                documents = _retrieval_result.get("retrieval_results", [])
                table_ddls = [document.get("table_ddl") for document in documents]
                sql_samples = sql_samples_task["formatted_output"].get("documents", [])
                instructions = instructions_task["formatted_output"].get(
                    "documents", []
                )

            if not self._is_stopped(query_id, self._ask_feedback_results):
                self._ask_feedback_results[query_id] = AskFeedbackResultResponse(
                    status="generating",
                    trace_id=trace_id,
                )

                text_to_sql_generation_results = await self._pipelines[
                    "sql_regeneration"
                ].run(
                    contexts=table_ddls,
                    sql_generation_reasoning=ask_feedback_request.sql_generation_reasoning,
                    sql=ask_feedback_request.sql,
                    project_id=ask_feedback_request.project_id,
                    configuration=ask_feedback_request.configurations,
                    sql_samples=sql_samples,
                    instructions=instructions,
                    has_calculated_field=has_calculated_field,
                    has_metric=has_metric,
                    sql_functions=sql_functions,
                )

                if sql_valid_result := text_to_sql_generation_results["post_process"][
                    "valid_generation_result"
                ]:
                    api_results = [
                        AskResult(
                            **{
                                "sql": sql_valid_result.get("sql"),
                                "type": "llm",
                            }
                        )
                    ]
                elif failed_dry_run_result := text_to_sql_generation_results[
                    "post_process"
                ]["invalid_generation_result"]:
                    if failed_dry_run_result["type"] != "TIME_OUT":
                        self._ask_feedback_results[
                            query_id
                        ] = AskFeedbackResultResponse(
                            status="correcting",
                            trace_id=trace_id,
                        )
                        sql_correction_results = await self._pipelines[
                            "sql_correction"
                        ].run(
                            contexts=[],
                            invalid_generation_result=failed_dry_run_result,
                            project_id=ask_feedback_request.project_id,
                        )

                        if valid_generation_result := sql_correction_results[
                            "post_process"
                        ]["valid_generation_result"]:
                            api_results = [
                                AskResult(
                                    **{
                                        "sql": valid_generation_result.get("sql"),
                                        "type": "llm",
                                    }
                                )
                            ]
                        elif failed_dry_run_result := sql_correction_results[
                            "post_process"
                        ]["invalid_generation_result"]:
                            invalid_sql = failed_dry_run_result["sql"]
                            error_message = failed_dry_run_result["error"]
                    else:
                        invalid_sql = failed_dry_run_result["sql"]
                        error_message = failed_dry_run_result["error"]

            if api_results:
                if not self._is_stopped(query_id, self._ask_feedback_results):
                    self._ask_feedback_results[query_id] = AskFeedbackResultResponse(
                        status="finished",
                        response=api_results,
                        trace_id=trace_id,
                    )
                results["ask_feedback_result"] = api_results
            else:
                logger.exception("ask feedback pipeline - NO_RELEVANT_SQL")
                if not self._is_stopped(query_id, self._ask_feedback_results):
                    self._ask_feedback_results[query_id] = AskFeedbackResultResponse(
                        status="failed",
                        error=AskError(
                            code="NO_RELEVANT_SQL",
                            message=error_message or "No relevant SQL",
                        ),
                        invalid_sql=invalid_sql,
                        trace_id=trace_id,
                    )
                results["metadata"]["error_type"] = "NO_RELEVANT_SQL"
                results["metadata"]["error_message"] = error_message

            return results

        except Exception as e:
            logger.exception(f"ask feedback pipeline - OTHERS: {e}")

            self._ask_feedback_results[query_id] = AskFeedbackResultResponse(
                status="failed",
                error=AskError(
                    code="OTHERS",
                    message=str(e),
                ),
                trace_id=trace_id,
            )

            results["metadata"]["error_type"] = "OTHERS"
            results["metadata"]["error_message"] = str(e)
            return results

    def stop_ask_feedback(
        self,
        stop_ask_feedback_request: StopAskFeedbackRequest,
    ):
        self._ask_feedback_results[
            stop_ask_feedback_request.query_id
        ] = AskFeedbackResultResponse(
            status="stopped",
        )

    def get_ask_feedback_result(
        self,
        ask_feedback_result_request: AskFeedbackResultRequest,
    ) -> AskFeedbackResultResponse:
        if (
            result := self._ask_feedback_results.get(
                ask_feedback_result_request.query_id
            )
        ) is None:
            logger.exception(
                f"ask feedback pipeline - OTHERS: {ask_feedback_result_request.query_id} is not found"
            )
            return AskFeedbackResultResponse(
                status="failed",
                error=AskError(
                    code="OTHERS",
                    message=f"{ask_feedback_result_request.query_id} is not found",
                ),
            )

        return result

    # Monitor Generation Methods
    @observe(name="Generate Monitor")
    @trace_metadata
    async def generate_monitor(
        self,
        generate_monitor_request: GenerateMonitorRequest,
        **kwargs,
    ):
        trace_id = kwargs.get("trace_id")
        query_id = generate_monitor_request.query_id

        try:
            if not self._is_stopped(query_id, self._monitor_generation_results):
                # Set initial status
                self._monitor_generation_results[query_id] = MonitorGenerationResultResponse(
                    status="understanding",
                    trace_id=trace_id,
                )

                # Create a status updater function
                def update_status(status: str):
                    if not self._is_stopped(query_id, self._monitor_generation_results):
                        self._monitor_generation_results[query_id].status = status
                        logger.info(f"Monitor generation {query_id}: Status updated to {status}")
                
                # Set the status updater in the pipeline
                pipeline = self._pipelines["monitor_generation"]
                pipeline.set_status_updater(update_status)
                
                try:
                    logger.info(f"Monitor generation {query_id}: Starting monitor generation pipeline")
                    result = await pipeline.run(
                        original_message=generate_monitor_request.original_message,
                        sql_reasoning=generate_monitor_request.sql_reasoning,
                        existing_sql=generate_monitor_request.existing_sql,
                        thread_data=generate_monitor_request.thread_data,
                        clarification_prompt=generate_monitor_request.clarification_prompt,
                        project_id=generate_monitor_request.project_id,
                        query_id=query_id,
                        trace_id=trace_id,
                    )
                finally:
                    # Clean up the status updater
                    pipeline.clear_status_updater()
                    logger.info(f"Monitor generation {query_id}: Pipeline execution completed")

                if not self._is_stopped(query_id, self._monitor_generation_results):
                    # Extract the result from the post_process step
                    monitor_result = result.get("post_process", result)
                    self._monitor_generation_results[query_id] = MonitorGenerationResultResponse(
                        status="finished",
                        response=MonitorGenerationResult(**monitor_result),
                        trace_id=trace_id,
                    )
                    logger.info(f"Monitor generation {query_id}: Completed successfully")

        except Exception as e:
            logger.exception(f"Monitor generation pipeline - OTHERS: {e}")
            self._monitor_generation_results[query_id] = MonitorGenerationResultResponse(
                status="failed",
                error=AskError(
                    code="OTHERS",
                    message=str(e),
                ),
                trace_id=trace_id,
            )

    def get_monitor_generation_result(
        self, query_id: str
    ) -> MonitorGenerationResultResponse:
        if (
            result := self._monitor_generation_results.get(query_id)
        ) is None:
            logger.exception(
                f"Monitor generation pipeline - OTHERS: {query_id} is not found"
            )
            return MonitorGenerationResultResponse(
                status="failed",
                error=AskError(
                    code="OTHERS",
                    message=f"{query_id} is not found",
                ),
            )

        return result

    async def stop_monitor_generation(self, query_id: str):
        if query_id in self._monitor_generation_results:
            self._monitor_generation_results[query_id].status = "stopped"



    @observe(name="Chat with Monitor")
    @trace_metadata
    async def chat_with_monitor(
        self,
        chat_request: ChatWithMonitorRequest,
        **kwargs,
    ):
        trace_id = kwargs.get("trace_id")
        query_id = chat_request.query_id

        try:
            if not self._is_stopped(query_id, self._monitor_generation_results):
                # Set initial status
                self._monitor_generation_results[query_id] = MonitorGenerationResultResponse(
                    status="understanding",
                    trace_id=trace_id,
                )

                # Create a status updater function
                def update_status(status: str):
                    if not self._is_stopped(query_id, self._monitor_generation_results):
                        self._monitor_generation_results[query_id].status = status
                        logger.info(f"Chat with monitor {query_id}: Status updated to {status}")
                
                # Set the status updater in the pipeline
                pipeline = self._pipelines["monitor_generation"]
                pipeline.set_status_updater(update_status)
                
                try:
                    logger.info(f"Chat with monitor {query_id}: Starting chat refinement pipeline")
                    result = await pipeline.chat_refinement(
                        original_message=chat_request.original_message,
                        existing_sql=chat_request.existing_sql,
                        chat_prompt=chat_request.chat_prompt,
                        thread_data=chat_request.thread_data,
                        project_id=chat_request.project_id,
                        query_id=query_id,
                        trace_id=trace_id,
                    )
                finally:
                    # Clean up the status updater
                    pipeline.clear_status_updater()
                    logger.info(f"Chat with monitor {query_id}: Pipeline execution completed")

                if not self._is_stopped(query_id, self._monitor_generation_results):
                    # Extract the result from the chat refinement
                    self._monitor_generation_results[query_id] = MonitorGenerationResultResponse(
                        status="finished",
                        response=MonitorGenerationResult(**result),
                        trace_id=trace_id,
                    )
                    logger.info(f"Chat with monitor {query_id}: Completed successfully")

        except Exception as e:
            logger.exception(f"Chat with monitor pipeline - OTHERS: {e}")
            self._monitor_generation_results[query_id] = MonitorGenerationResultResponse(
                status="failed",
                error=AskError(
                    code="OTHERS",
                    message=str(e),
                ),
                trace_id=trace_id,
            )
