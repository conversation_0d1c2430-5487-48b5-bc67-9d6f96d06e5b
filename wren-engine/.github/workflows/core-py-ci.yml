name: Core Python CI
permissions:
  contents: read
  pull-requests: write

on:
  pull_request:
    paths:
      - 'wren-core-py/**'
      - 'wren-core/**'

defaults:
  run:
    working-directory: wren-core-py

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number }}
  cancel-in-progress: true

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: 3.11
      - name: <PERSON><PERSON> Cargo
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            ./wren-core-py/target/
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
      - name: Install poetry
        run: pipx install poetry
      - name: Install dependencies
        run: poetry install --no-root
      - name: Test Rust and Python code
        run: |
          cargo test
          poetry run maturin develop
          poetry run pytest
