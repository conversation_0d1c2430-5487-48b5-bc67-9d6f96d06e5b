name: Build image
permissions:
  contents: write
  packages: write
  pull-requests: write

on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      docker_image_tag_name:
        type: string
        description: Docker image tag name (Optional)

env:
  ENGINE_IMAGE: ghcr.io/canner/wren-engine
  IBIS_IMAGE: ghcr.io/canner/wren-engine-ibis

jobs:
  prepare-tag:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Prepare tag name
        id: prepare_tag
        run: |
          if [ "${{ github.event_name }}" = "push" ]; then
            echo "type=sha" > tags.txt
            echo "type=schedule" >> tags.txt
          fi
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            if [ -n "${{ github.event.inputs.docker_image_tag_name }}" ]; then
              tag_name=${{ github.event.inputs.docker_image_tag_name }}
            else
              tag_name=$(echo ${{ github.ref_name }} | sed 's/[^a-zA-Z0-9]/-/g')-$(git log -1 --pretty=%h)
            fi
            echo "type=raw,value=$tag_name" > tags.txt
          fi
          echo "tags=$(cat tags.txt)" >> $GITHUB_OUTPUT
    outputs:
      tags: ${{ steps.prepare_tag.outputs.tags }}
  build-image:
    needs: prepare-tag
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '21'
          cache: 'maven'
      - name: Build
        working-directory: wren-core-legacy
        run: |
          ./mvnw clean install -B -DskipTests -P exec-jar
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.ENGINE_IMAGE }}
          tags: |
            ${{ needs.prepare-tag.outputs.tags }}
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Prepare
        working-directory: wren-core-legacy
        id: prepare
        run: |
          WREN_VERSION=$(./mvnw --quiet help:evaluate -Dexpression=project.version -DforceStdout)
          cp ./wren-server/target/wren-server-${WREN_VERSION}-executable.jar ./docker
          echo "WREN_VERSION=$WREN_VERSION" >> "$GITHUB_OUTPUT"
      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: ./wren-core-legacy/docker
          platforms: linux/amd64,linux/arm64
          tags: ${{ steps.meta.outputs.tags }}
          build-args: |
            WREN_VERSION=${{ steps.prepare.outputs.WREN_VERSION }}
          push: true
  build-ibis-image:
    needs: prepare-tag
    strategy:
      fail-fast: false
      matrix:
        arch:
          - runner: ubuntu-latest
            platform: linux/amd64
          - runner: linux_arm64_runner
            platform: linux/arm64
    runs-on: ${{ matrix.arch.runner }}
    steps:
      - name: Prepare platform
        run: |
          platform=${{ matrix.arch.platform }}
          echo "PLATFORM_PAIR=${platform//\//-}" >> $GITHUB_ENV
      - uses: actions/checkout@v4
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.IBIS_IMAGE }}
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push by digest
        id: build
        uses: docker/build-push-action@v6
        with:
          platforms: ${{ matrix.arch.platform }}
          labels: ${{ steps.meta.outputs.labels }}
          context: ./ibis-server
          build-contexts: |
            wren-core-py=./wren-core-py
            wren-core=./wren-core
            wren-core-base=./wren-core-base
          outputs: type=image,name=${{ env.IBIS_IMAGE }},push-by-digest=true,name-canonical=true,push=true
      - name: Export digest
        run: |
          mkdir -p /tmp/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "/tmp/digests/${digest#sha256:}"
      - name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-${{ env.PLATFORM_PAIR }}
          path: /tmp/digests/*
          if-no-files-found: error
          retention-days: 1
  merge:
    runs-on: ubuntu-latest
    needs: [ prepare-tag, build-ibis-image ]
    steps:
      - name: Download digests
        uses: actions/download-artifact@v4
        with:
          path: /tmp/digests
          pattern: digests-*
          merge-multiple: true
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.IBIS_IMAGE }}
          tags: |
            ${{ needs.prepare-tag.outputs.tags }}
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Create manifest list and push
        working-directory: /tmp/digests
        run: |
          TAGS=$(echo "${{ steps.meta.outputs.tags }}" | awk '{printf "--tag %s ", $0}')
          docker buildx imagetools create $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
            $(printf '${{ env.IBIS_IMAGE }}@sha256:%s ' *) \
            $TAGS
      - name: Inspect image
        run: |
          docker buildx imagetools inspect ${{ steps.meta.outputs.tags }}
