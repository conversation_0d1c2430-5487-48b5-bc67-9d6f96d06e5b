name: Maven tests
permissions:
  contents: read
  pull-requests: write

on:
  pull_request:
    types:
      - opened
      - edited
      - synchronize
      - reopened
      - labeled
      - unlabeled
    paths:
      - 'wren-core-legacy/**'

defaults:
  run:
    working-directory: wren-core-legacy

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number }}
  cancel-in-progress: true

jobs:
  maven-tests:
    runs-on: ubuntu-latest
    if: contains(github.event.pull_request.labels.*.name, 'v1-engine-changed')
    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '21'
          cache: 'maven'
      - name: build
        run: |
          ./mvnw clean install -B -DskipTests
      - name: unit tests
        run: |
          ./mvnw test -B --fail-at-end -pl !:wren-tests
      - name: integration tests
        env:
          TEST_BIG_QUERY_PROJECT_ID: ${{ secrets.TEST_BIG_QUERY_PROJECT_ID }}
          TEST_BIG_QUERY_PARENT_PROJECT_ID: ${{ secrets.TEST_BIG_QUERY_PARENT_PROJECT_ID }}
          TEST_BIG_QUERY_CREDENTIALS_BASE64_JSON: ${{ secrets.TEST_BIG_QUERY_CREDENTIALS_BASE64_JSON }}
          TEST_BIG_QUERY_BUCKET_NAME: ${{ secrets.TEST_BIG_QUERY_BUCKET_NAME }}
          TEST_DUCKDB_STORAGE_ACCESS_KEY: ${{ secrets.TEST_DUCKDB_STORAGE_ACCESS_KEY }}
          TEST_DUCKDB_STORAGE_SECRET_KEY: ${{ secrets.TEST_DUCKDB_STORAGE_SECRET_KEY }}
        run: |
          ./mvnw test -B --fail-at-end -pl :wren-tests
