name: Stable Release
permissions:
  contents: write
  packages: write
  pull-requests: write

on:
  workflow_dispatch:
    inputs:
      specific_version:
        type: string
        description: Specific version number (Optional). Default will be the current version plus 0.0.1.

env:
  ENGINE_IMAGE: ghcr.io/canner/wren-engine
  IBIS_IMAGE: ghcr.io/canner/wren-engine-ibis

jobs:
  prepare-version:
    runs-on: ubuntu-latest
    steps:
      - name: Generate a token
        id: generate-token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ vars.CI_APP_ID }}
          private-key: ${{ secrets.CI_APP_PRIVATE_KEY }}
      - uses: actions/checkout@v4
        with:
          token: ${{ steps.generate-token.outputs.token }}
      - name: Set up Git
        run: |
          git config --global user.name "wren-ai[bot]"
          git config --global user.email "<EMAIL>"
      - uses: actions/setup-python@v5
        with:
          python-version-file: ./ibis-server/pyproject.toml
      - uses: abatilo/actions-poetry@v3
        with:
          poetry-version: 1.7.1
      - name: Prepare next version
        id: next_version
        working-directory: ibis-server
        run: |
          if [ -n "${{ github.event.inputs.specific_version }}" ]; then
            poetry version --next-phase ${{ github.event.inputs.specific_version }}
          else
            poetry version patch
          fi
          version=$(poetry version | awk '{print $2}')
          git add pyproject.toml
          git commit -m "Upgrade ibis version to $version"
          git push
          echo "value=$version" >> $GITHUB_OUTPUT
      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '21'
          cache: 'maven'
      - name: Maven Prepare Release
        working-directory: wren-core-legacy
        id: maven_prepare_release
        run: |
          if [ -n "${{ github.event.inputs.specific_version }}" ]; then
            version_number=${{ github.event.inputs.specific_version }}
            ./mvnw release:prepare -B -DreleaseVersion=${{ github.event.inputs.specific_version }}
          else
            version_number=$(./mvnw --quiet help:evaluate -Dexpression=project.version -DforceStdout | sed -n 's/^\(.*\)-SNAPSHOT/\1/p')
            ./mvnw release:prepare -B -DreleaseVersion=${version_number}
          fi
          # push the release commit and tag from maven release:prepare
          git push
          echo "version_number=$version_number" >> $GITHUB_OUTPUT
    outputs:
      next_version: ${{ steps.next_version.outputs.value }}
      maven_version: ${{ steps.maven_prepare_release.outputs.version_number }}
  gh-releasse:
    needs: prepare-version
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ needs.prepare-version.outputs.next_version }}
          generate_release_notes: true
          make_latest: "true"
  stable-release-wren-engine:
    needs: prepare-version
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '21'
          cache: 'maven'
      - name: Build
        working-directory: wren-core-legacy
        run: |
          ./mvnw clean install -B -DskipTests -P exec-jar
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.ENGINE_IMAGE }}
          tags: |
            type=raw,value=${{ needs.prepare-version.outputs.maven_version }}
            type=raw,value=latest
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Prepare
        working-directory: wren-core-legacy
        id: prepare
        run: |
          WREN_VERSION=$(./mvnw --quiet help:evaluate -Dexpression=project.version -DforceStdout)
          cp ./wren-server/target/wren-server-${WREN_VERSION}-executable.jar ./docker
          echo "WREN_VERSION=$WREN_VERSION" >> "$GITHUB_OUTPUT"
      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: ./wren-core-legacy/docker
          platforms: linux/amd64,linux/arm64
          tags: ${{ steps.meta.outputs.tags }}
          build-args: |
            WREN_VERSION=${{ steps.prepare.outputs.WREN_VERSION }}
          push: true
  stable-release-ibis:
    needs: prepare-version
    strategy:
      fail-fast: false
      matrix:
        arch:
          - runner: ubuntu-latest
            platform: linux/amd64
          - runner: linux_arm64_runner
            platform: linux/arm64
    runs-on: ${{ matrix.arch.runner }}
    steps:
      - name: Prepare platform
        run: |
          platform=${{ matrix.arch.platform }}
          echo "PLATFORM_PAIR=${platform//\//-}" >> $GITHUB_ENV
      - uses: actions/checkout@v4
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.IBIS_IMAGE }}
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push by digest
        id: build
        uses: docker/build-push-action@v6
        with:
          platforms: ${{ matrix.arch.platform }}
          labels: ${{ steps.meta.outputs.labels }}
          context: ./ibis-server
          build-args: |
            ENV=prod
          build-contexts: |
            wren-core-py=./wren-core-py
            wren-core=./wren-core
            wren-core-base=./wren-core-base
          outputs: type=image,name=${{ env.IBIS_IMAGE }},push-by-digest=true,name-canonical=true,push=true
      - name: Export digest
        run: |
          mkdir -p /tmp/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "/tmp/digests/${digest#sha256:}"
      - name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-${{ env.PLATFORM_PAIR }}
          path: /tmp/digests/*
          if-no-files-found: error
          retention-days: 1
  merge:
    runs-on: ubuntu-latest
    needs: [ prepare-version, stable-release-ibis ]
    steps:
      - name: Download digests
        uses: actions/download-artifact@v4
        with:
          path: /tmp/digests
          pattern: digests-*
          merge-multiple: true
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.IBIS_IMAGE }}
          tags: |
            type=raw,value=${{ needs.prepare-version.outputs.next_version }}
            type=raw,value=latest
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Create manifest list and push
        working-directory: /tmp/digests
        run: |
          TAGS=$(echo "${{ steps.meta.outputs.tags }}" | awk '{printf "--tag %s ", $0}')
          docker buildx imagetools create $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
            $(printf '${{ env.IBIS_IMAGE }}@sha256:%s ' *) \
            $TAGS
