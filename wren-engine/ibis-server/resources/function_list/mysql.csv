function_type,name,return_type,param_names,param_types,description
scalar,if,bool,,"boolean,any,any","Returns one value if a condition is TRUE, or another value if a condition is FALSE"
scalar,ceiling,int,,"decimal","Returns the smallest integer value greater than or equal to a number"
scalar,datediff,int,,"date,date","Returns the number of days between two dates"
scalar,timestampdiff,int,,"varchar,datetime,datetime","Returns the difference between two datetime expressions"
scalar,inet_aton,int,,"varchar","Converts an IPv4 address to numeric value"
scalar,inet_ntoa,varchar,,"int","Converts numeric value to IPv4 address"
scalar,format,varchar,,"decimal,int","Formats number to specified decimal places and adds thousand separators"
scalar,hex,varchar,,"decimal_or_string","Returns hexadecimal representation of a decimal or string value"
scalar,unhex,varchar,,"varchar","Converts hexadecimal value to string"
scalar,lcase,varchar,,"varchar","Synonym for LOWER()"
scalar,quote,varchar,,"varchar","Escapes string and adds single quotes"
scalar,soundex,varchar,,"varchar","Returns soundex string of given string"
scalar,space,varchar,,"int","Returns string of specified number of spaces"
scalar,truncate,decimal,,"decimal,int","Truncates number to specified number of decimal places"
scalar,weekday,int,,"date","Returns weekday index (0=Monday, 6=Sunday)"
scalar,yearweek,int,,"date","Returns year and week number"
scalar,dayname,varchar,,"date","Returns name of weekday"
scalar,monthname,varchar,,"date","Returns name of month"
scalar,quarter,int,,"date","Returns quarter from date (1 to 4)"
scalar,week,int,,"date","Returns week number"
aggregate,group_concat,varchar,,"any","Returns a concatenated string from a group"
aggregate,std,decimal,,"any","Returns the population standard deviation"
aggregate,variance,decimal,,"any","Returns the population variance"
aggregate,json_arrayagg,json,,"any","Aggregates result set as JSON array"
aggregate,json_objectagg,json,,"varchar,any","Aggregates result set as JSON object"
