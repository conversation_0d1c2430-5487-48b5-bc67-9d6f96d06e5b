function_type,name,return_type,param_names,param_types,description
aggregate,every,boolean,,boolean,"Equivalent to bool_and"
aggregate,json_object_agg,json,,"text,any","Aggregates pairs into JSON object"
aggregate,jsonb_object_agg,jsonb,,"text,any","Aggregates pairs into JSONB object"
aggregate,xmlagg,xml,,xml,"Concatenates XML values"
scalar,age,interval,,"timestamp,timestamp","Difference between timestamps"
scalar,array_lower,int,,"array,integer","Lower bound of array"
scalar,array_to_json,json,,array,"Convert array to JSON"
scalar,array_upper,int,,"array,integer","Upper bound of array"
scalar,convert_from,text,,"bytea,text","Convert from encoding"
scalar,convert_to,bytea,,"text,text","Convert to encoding"
scalar,extract,numeric,,"text,timestamp","Get subfield from date/time"
scalar,format,text,,"text,array<any>","Format string"
scalar,host,text,,inet,"Extract host from IP address"
scalar,isfinite,boolean,,timestamp,"Test for finite date/timestamp/interval"
scalar,json_array_length,int,,json,"Length of JSON array"
scalar,json_extract_path,json,,"json,array<text>","Get JSON object at path"
scalar,json_object_keys,array<text>,,json,"Get JSON object keys"
scalar,jsonb_array_length,int,,jsonb,"Length of JSONB array"
scalar,jsonb_extract_path,jsonb,,"jsonb,array<text>","Get JSONB object at path"
scalar,jsonb_object_keys,array<text>,,jsonb,"Get JSONB object keys"
scalar,mod,numeric,,"numeric,numeric","Modulo (remainder)"
scalar,parse_ident,array<text>,,"text,boolean","Parse qualified identifier"
scalar,pg_client_encoding,name,,,"Current client encoding"
scalar,pg_get_expr,text,,"pg_node_tree,oid","Decompile internal form of expression"
scalar,pg_get_viewdef,text,,"oid","Get view definition"
scalar,quote_ident,text,,text,"Quote identifier"
scalar,quote_literal,text,,any,"Quote literal"
scalar,quote_nullable,text,,any,"Quote nullable"
scalar,regexp_split_to_array,array<text>,,"text,text","Split string by pattern"
scalar,regexp_split_to_table,array<text>,,"text,text","Split string by pattern"
scalar,sign,numeric,,numeric,"Sign of number"
scalar,to_json,json,,boolean,"Convert to JSON"
scalar,to_number,numeric,,"text,text","Convert string to number"
scalar,unistr,varchar,,text,"Postgres: Evaluate escaped Unicode characters in the argument"
