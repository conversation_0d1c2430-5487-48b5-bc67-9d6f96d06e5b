function_type,name,return_type,param_names,param_types,description
aggregate,countif,int64,,boolean,"Counts the rows where a condition is true."
aggregate,any_value,same_as_input,,"any","Returns any arbitrary value from the input values."
scalar,format,text,,"text","Formats values into a string."
scalar,safe_divide,float,,"float,float","Divides two numbers, returning NULL if the divisor is zero."
scalar,safe_multiply,float,,"float,float","Multiplies two numbers, returning NULL if an overflow occurs."
scalar,safe_add,float,,"float,float","Adds two numbers, returning NULL if an overflow occurs."
scalar,safe_subtract,float,,"float,float","Subtracts two numbers, returning NULL if an overflow occurs."
scalar,current_datetime,timestamp,,"","Returns current date and time."
scalar,date_add,date,,"date,int64","Adds a number of day to a date."
scalar,date_sub,date,,"date,interval","Subtracts a specified interval from a date."
scalar,date_diff,int64,,"date,date,granularity","Returns the difference between two dates."
scalar,timestamp_add,timestamp,,"timestamp,granularity","Adds a specified interval to a timestamp."
scalar,timestamp_sub,timestamp,,"timestamp,granularity","Subtracts a specified interval from a timestamp."
scalar,timestamp_diff,int64,,"timestamp,timestamp,granularity","Returns the difference between two timestamps."
scalar,timestamp_trunc,timestamp,,"timestamp,granularity","Truncates a timestamp to a specified granularity."
scalar,timestamp_micros,timestamp,,"int64","Converts the number of microseconds since 1970-01-01 00:00:00 UTC to a TIMESTAMP."
scalar,timestamp_millis,timestamp,,"int64","Converts the number of milliseconds since 1970-01-01 00:00:00 UTC to a TIMESTAMP."
scalar,timestamp_seconds,timestamp,,"int64","Converts the number of seconds since 1970-01-01 00:00:00 UTC to a TIMESTAMP."
scalar,timestamp,timestamp,,"text","Converts a string to a TIMESTAMP."
scalar,format_date,string,,"string,date","Formats a date according to the specified format string."
scalar,format_timestamp,string,,"string,timestamp","Formats a timestamp according to the specified format string."
scalar,parse_date,date,,"text,text","Parses a date from a string."
scalar,parse_datetime,datetime,,"text,text","Converts a STRING value to a DATETIME value."
scalar,json_query,text,,"json,text","Extracts a JSON value from a JSON string."
scalar,json_value,text,,"json,text","Extracts a scalar JSON value as a string."
scalar,json_query_array,array,,"json,text","Extracts a JSON array from a JSON string."
scalar,json_value_array,array,,"json,text","Extracts an array of scalar JSON values as strings."
scalar,lax_bool,boolean,,"any","Converts a value to boolean with relaxed type checking."
scalar,lax_float64,float,,"any","Converts a value to float with relaxed type checking."
scalar,lax_int64,int64,,"any","Converts a value to int with relaxed type checking."
scalar,lax_string,text,,"any","Converts a value to text with relaxed type checking."
scalar,bool,boolean,,"any","Converts a JSON value to SQL boolean type."
scalar,float64,float,,"any","Converts a JSON value to SQL float type."
scalar,int64,int64,,"any","Converts a JSON value to SQL int type."
scalar,string,text,,"any","Converts a JSON value to SQL text type."
