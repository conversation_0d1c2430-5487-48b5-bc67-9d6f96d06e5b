function_type,name,return_type,description
aggregate,any_value,same_as_input,"Returns any value from the group"
aggregate,approx_count_distinct,numeric,"Approximates count of distinct values"
aggregate,approx_percentile,numeric,"Approximates percentile of expression"
aggregate,listagg,string,"Concatenates values with delimiter"
aggregate,mode,same_as_input,"Returns most frequent value"
aggregate,variance,numeric,"Returns variance"
scalar,add_months,date,"Adds months to date"
scalar,array_compact,array,"Removes null values from array"
scalar,array_insert,array,"Inserts element into array"
scalar,array_size,numeric,"Returns number of elements in array"
scalar,base64_encode,string,"Encodes binary to base64"
scalar,base64_decode,binary,"Decodes base64 to binary"
scalar,char,string,"Returns character from ASCII code"
scalar,charindex,numeric,"Returns position of substring"
scalar,date_add,timestamp,"Adds interval to date"
scalar,date_sub,timestamp,"Subtracts interval from date"
scalar,dayname,string,"Returns name of day"
scalar,dayofmonth,numeric,"Returns day of month (1-31)"
scalar,dayofweek,numeric,"Returns day of week (0-6)"
scalar,dayofyear,numeric,"Returns day of year (1-366)"
scalar,div0,numeric,"Divides with zero handling"
scalar,extract,numeric,"Extracts part from date/time"
scalar,hash,numeric,"Returns hash value"
scalar,hex_encode,string,"Encodes binary to hex"
scalar,hex_decode,binary,"Decodes hex to binary"
scalar,iff,same_as_input,"Returns value based on condition"
scalar,is_array,boolean,"Tests if value is array"
scalar,is_decimal,boolean,"Tests if value is decimal"
scalar,is_integer,boolean,"Tests if value is integer"
scalar,is_null_value,boolean,"Tests if variant is SQL NULL"
scalar,is_object,boolean,"Tests if value is object"
scalar,is_real,boolean,"Tests if value is real"
scalar,is_string,boolean,"Tests if value is string"
scalar,json_extract_path_text,string,"Extracts text from JSON path"
scalar,last_day,date,"Returns last day of month"
scalar,len,numeric,"Returns length of string"
scalar,months_between,numeric,"Returns months between dates"
scalar,monthname,string,"Returns name of month"
scalar,parse_json,string,"Parses JSON string"
scalar,parse_url,string,"Parses URL components"
scalar,parse_xml,string,"Parses XML string"
scalar,regexp_substr,string,"Extracts regex match"
scalar,sha1,string,"Returns SHA1 hash"
scalar,sha2,string,"Returns SHA2 hash"
scalar,split,array,"Splits string into array"
scalar,startswith,boolean,"Tests if string starts with prefix"
scalar,time_slice,timestamp,"Truncates timestamp to interval"
scalar,timeadd,timestamp,"Adds interval to timestamp"
scalar,to_array,array,"Converts value to array"
scalar,to_json,string,"Converts to JSON string"
scalar,to_number,numeric,"Converts to number"
scalar,to_time,time,"Converts to time"
scalar,to_xml,string,"Converts to XML string"
scalar,try_cast,same_as_input,"Safe type conversion"
scalar,try_to_date,date,"Safe date conversion"
scalar,try_to_number,numeric,"Safe number conversion"
scalar,try_to_timestamp,timestamp,"Safe timestamp conversion"
scalar,uuid_string,string,"Generates UUID"
scalar,xmlget,xml,"Gets XML element"
window,ratio_to_report,numeric,"Returns ratio to sum"
