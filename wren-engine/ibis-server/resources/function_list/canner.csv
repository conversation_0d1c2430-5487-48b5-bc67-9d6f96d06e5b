function_type,name,return_type,param_names,param_types,description
aggregate,approx_percentile,same_as_input,,,"Approximates percentile"
aggregate,approximate_distinct,bigint,,,"Approximates count of distinct values"
aggregate,regr_slope,double,,,"Returns linear regression slope"
scalar,date_add,date,,,"Adds interval to date"
scalar,date_sub,date,,,"Subtracts interval from date"
scalar,filter,same_as_input,,,"Filters array using lambda"
scalar,format_datetime,varchar,,,"Formats datetime according to format string"
scalar,from_base64,varbinary,,,"Converts base64 to binary"
scalar,from_hex,varbinary,,,"Converts hex string to binary"
scalar,hamming_distance,bigint,,,"Calculates Hamming distance"
scalar,is_finite,boolean,,,"Tests if value is finite"
scalar,is_infinite,boolean,,,"Tests if value is infinite"
scalar,is_nan,boolean,,,"Tests if value is NaN"
scalar,json_extract,json,,,"Extracts JSON by JSONPath"
scalar,json_format,varchar,,,"Pretty prints JSON"
scalar,json_parse,json,,,"Parses string as JSON"
scalar,levenshtein_distance,bigint,,,"Calculates Levenshtein distance"
scalar,parse_datetime,timestamp,,,"Parses string to datetime using format"
scalar,reduce,same_as_input,,,"Reduces array to single value using lambda"
scalar,regexp_extract,varchar,,,"Extracts substring using regex"
scalar,split,array<string>,,,"Splits string by delimiter into array"
scalar,to_base64,varchar,,,"Converts binary to base64"
scalar,transform,same_as_input,,,"Applies lambda to each element"
scalar,try,same_as_input,,,"Returns null if evaluation fails"
scalar,url_decode,varchar,,,"Decodes URL encoded string"
scalar,url_encode,varchar,,,"URL encodes string"
scalar,word_stem,varchar,,,"Returns word stem (English only)"
scalar,xxhash64,bigint,,,"Computes xxHash64 hash"
scalar,zip_with,array,,,"Combines two arrays using lambda"
window,lead,any,,,"Returns value from following row"
