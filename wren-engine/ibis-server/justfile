default:
    @just --list --unsorted

build-core:
    cd ../wren-core-py && just install && just build

core-wheel-path := "../wren-core-py/target/wheels/wren_core_py-*.whl"

install-core:
    just build-core
    # Using pip install to avoid adding wheel to pyproject.toml
    poetry run pip install --force-reinstall {{ core-wheel-path }}

install *args:
    poetry install {{ args }}
    just install-core

pre-commit-install:
    poetry run pre-commit install

port := "8000"

run:
    poetry run fastapi run --port {{ port }}

workers := "2"

run-gunicorn:
    gunicorn app.main:app --bind 0.0.0.0:{{ port }} \
        -k app.worker.WrenUvicornWorker \
        --workers {{ workers }} \
        --max-requests 1000 \
        --max-requests-jitter 100 \
        --timeout 300 \
        --graceful-timeout 60

run-trace-console:
    opentelemetry-instrument --traces_exporter console --metrics_exporter none fastapi run --port {{ port }}

run-trace-otlp:
    opentelemetry-instrument \
        --traces_exporter otlp \
        --exporter_otlp_endpoint http://localhost:4318 \
        --exporter_otlp_protocol http/protobuf \
        --service_name wren-engine \
        fastapi run --port {{ port }}
dev:
    poetry run fastapi dev --port {{ port }}

# run the pytest tests for the given marker
test MARKER:
    poetry run pytest -m '{{ MARKER }}'

test-verbose MARKER:
    poetry run pytest -s -v -m '{{ MARKER }}'

image-name := "ghcr.io/canner/wren-engine-ibis:latest"

docker-build:
    docker buildx build -t {{ image-name }} -f Dockerfile \
        --build-context wren-core-py=../wren-core-py \
        --build-context wren-core=../wren-core \
        --build-context wren-core-base=../wren-core-base \
        .

docker-run:
    docker run -it --rm -p 8000:8000 --env-file .env {{ image-name }}

compose-up:
    docker compose -f ../docker/compose.yaml up

compose-down:
    docker compose -f ../docker/compose.yaml down

lint:
    poetry run ruff format -q . --check
    poetry run ruff check .

format:
    poetry run ruff format .
    poetry run ruff check --fix .
    taplo fmt

alias dbuild := docker-build
alias drun := docker-run
alias fmt := format
