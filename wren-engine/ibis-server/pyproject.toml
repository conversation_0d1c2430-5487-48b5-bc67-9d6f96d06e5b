[tool.poetry]
name = "wren-engine"
version = "0.16.4"
description = ""
authors = ["Canner <<EMAIL>>"]
readme = "README.md"
packages = [{ include = "app" }]

[tool.poetry.dependencies]
python = ">=3.11,<3.12"
fastapi = { version = "0.115.12", extras = ["standard"] }
pydantic = "2.10.6"
ibis-framework = { version = "10.6.0", extras = [
  "athena",
  "bigquery",
  "clickhouse",
  "mssql",
  "mysql",
  "oracle",
  "postgres",
  "snowflake",
  "trino",
] }
google-auth = "2.38.0"
httpx = "0.28.1"
python-dotenv = "1.0.1"
orjson = "3.10.16"
pandas = "2.2.3"
geopandas = "^1.0.1"
geoalchemy2 = "^0.17.1"
sqlglot = { extras = [
  "rs",
], version = ">=23.4,<26.5" } # the version should follow the ibis-framework
loguru = "0.7.3"
asgi-correlation-id = "4.3.4"
gql = { extras = ["aiohttp"], version = "3.5.2" }
anyio = "4.9.0"
duckdb = "1.2.1"
opendal = ">=0.45"
oracledb = "3.0.0"
mysqlclient = { version = ">=2.2.4,<3", optional = true }
opentelemetry-api = ">=1.30.0"
opentelemetry-sdk = ">=1.30.0"
uvicorn = "^0.34.0"
gunicorn = "^23.0.0"
uvicorn-worker = "^0.3.0"
jinja2 = ">=3.1.6"
redshift_connector = "2.1.7"

[tool.poetry.group.dev.dependencies]
pytest = "8.3.5"
testcontainers = { version = "4.9.2", extras = [
  "clickhouse",
  "mssql",
  "mysql",
  "postgres",
  "trino",
  "minio",
] }
sqlalchemy = "2.0.39"
pre-commit = "4.2.0"
ruff = "0.11.2"
trino = ">=0.321,<1"
psycopg2 = ">=2.8.4,<3"
clickhouse-connect = "0.8.15"
asgi-lifespan = "2.1.0"

[tool.pytest.ini_options]
addopts = ["--strict-markers"]
markers = [
  "athena: mark a test as an athena test",
  "bigquery: mark a test as a bigquery test",
  "canner: mark a test as a canner test",
  "clickhouse: mark a test as a clickhouse test",
  "functions: mark a test as a functions test",
  "mssql: mark a test as a mssql test",
  "mysql: mark a test as a mysql test",
  "oracle: mark a test  as a oracle test",
  "postgres: mark a test as a postgres test",
  "redshift: mark a test as a redshift test",
  "snowflake: mark a test as a snowflake test",
  "trino: mark a test as a trino test",
  "local_file: mark a test as a local file test",
  "s3_file: mark a test as a s3 file test",
  "minio_file: mark a test as a minio file test",
  "gcs_file: mark a test as a gcs file test",
  "beta: mark a test as a test for beta versions of the engine",
]

[tool.ruff]
line-length = 88
target-version = "py311"
exclude = ["tools/"]

[tool.ruff.lint]
select = [
  "C4",  # comprehensions
  "D",   # pydocstyle
  "E",   # pycodestyle
  "EXE", # flake8-executable
  "F",   # pyflakes
  "FA",  # flake8-future-annotations
  "G",   # flake8-logging-format
  "FLY", # flynt (format string conversion)
  "I",   # isort
  "ICN", # flake8-import-conventions
  "INP", # flake8-no-pep420 (implicit namespace packages)
  "ISC", # flake8-implicit-str-concat
  "PGH", # pygrep-hooks
  "PIE", # flake8-pie
  "PL",  # pylint
  "RET", # flake8-return
  "RUF", # ruff-specific rules
  "SIM", # flake8-simplify
  "T10", # flake8-debugger
  "T20", # flake8-print
  "TID", # flake8-tidy-imports
  "UP",  # pyupgrade
  "YTT", # flake8-2020
]
ignore = [
  "B008",    # do not perform function calls in argument defaults
  "B028",    # required stacklevel argument to warn
  "B904",    # raise from e or raise from None in exception handlers
  "B905",    # zip-without-explicit-strict
  "C408",    # dict(...) as literal
  "C901",    # too complex
  "D100",    # public module
  "D101",    # public class
  "D102",    # public method
  "D103",    # public function
  "D104",    # public package
  "D105",    # magic methods
  "D106",    # nested class
  "D107",    # init
  "D202",    # blank lines after function docstring
  "D203",    # blank line before class docstring
  "D213",    # Multi-line docstring summary should start at the second line
  "D401",    # Imperative mood
  "D402",    # First line should not be the function's signature
  "D413",    # Blank line required after last section
  "E501",    # line-too-long, this is automatically enforced by ruff format
  "E731",    # lambda-assignment
  "ISC001",  # single line implicit string concat, handled by ruff format
  "PGH003",  # blanket-type-ignore
  "PLC0105", # covariant type parameters should have a _co suffix
  "PLR0124", # name compared with self, e.g., a == a
  "PLR0911", # too many return statements
  "PLR0912", # too many branches
  "PLR0913", # too many arguments
  "PLR0915", # too many statements
  "PLR2004", # forces everything to be a constant
  "PLW2901", # overwriting loop variable
  "RET504",  # unnecessary-assign, these are useful for debugging
  "RET505",  # superfluous-else-return, stylistic choice
  "RET506",  # superfluous-else-raise, stylistic choice
  "RET507",  # superfluous-else-continue, stylistic choice
  "RET508",  # superfluous-else-break, stylistic choice
  "RUF005",  # splat instead of concat
  "RUF012",  # Mutable class attributes should be annotated with `typing.ClassVar`
  "S101",    # ignore "Use of `assert` detected"
  "SIM102",  # nested ifs
  "SIM108",  # convert everything to ternary operator
  "SIM114",  # combine `if` branches using logical `or` operator
  "SIM116",  # dictionary instead of `if` statements
  "SIM117",  # nested with statements
  "SIM118",  # remove .keys() calls from dictionaries
  "SIM300",  # yoda conditions
  "UP007",   # Optional[str] -> str | None
  "UP038",   # non-pep604-isinstance, results in slower code
  "W191",    # indentation contains tabs
]
# none of these codes will be automatically fixed by ruff
unfixable = [
  "T201",   # print statements
  "F401",   # unused imports
  "RUF100", # unused noqa comments
  "F841",   # unused variables
]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
