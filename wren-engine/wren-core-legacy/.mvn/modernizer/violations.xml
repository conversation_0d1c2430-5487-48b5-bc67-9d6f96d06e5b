<?xml version="1.0"?>
<modernizer>
    <violation>
        <name>java/lang/Class.newInstance:()Ljava/lang/Object;</name>
        <version>1.1</version>
        <comment>Prefer Class.getConstructor().newInstance()</comment>
    </violation>

    <violation>
        <name>java/lang/String."&lt;init&gt;":([B)V</name>
        <version>1.1</version>
        <comment>Prefer new String(byte[], Charset)</comment>
    </violation>

    <violation>
        <name>java/lang/String.getBytes:()[B</name>
        <version>1.1</version>
        <comment>Prefer String.getBytes(Charset)</comment>
    </violation>

    <violation>
        <name>java/lang/String.toLowerCase:()Ljava/lang/String;</name>
        <version>1.1</version>
        <comment>Prefer String.toLowerCase(java.util.Locale)</comment>
    </violation>

    <violation>
        <name>java/lang/String.toUpperCase:()Ljava/lang/String;</name>
        <version>1.1</version>
        <comment>Prefer String.toUpperCase(java.util.Locale)</comment>
    </violation>

    <violation>
        <!-- File.getPath() is the canonical way to convert File to its String representation suitable for passing to new File() -->
        <name>java/io/File.toString:()Ljava/lang/String;</name>
        <version>1.1</version>
        <comment>Prefer File.getPath()</comment>
    </violation>

    <violation>
        <name>com/google/common/primitives/Ints.checkedCast:(J)I</name>
        <version>1.8</version>
        <comment>Prefer Math.toIntExact(long)</comment>
    </violation>

    <violation>
        <name>org/testng/Assert.assertEquals:(Ljava/lang/Iterable;Ljava/lang/Iterable;)V</name>
        <version>1.8</version>
        <comment>Use io.trino.testing.assertions.Assert.assertEquals due to TestNG #543</comment>
    </violation>

    <violation>
        <name>org/testng/Assert.assertEquals:(Ljava/lang/Iterable;Ljava/lang/Iterable;Ljava/lang/String;)V</name>
        <version>1.8</version>
        <comment>Use io.trino.testing.assertions.Assert.assertEquals due to TestNG #543</comment>
    </violation>

    <violation>
        <name>org/testng/Assert.assertThrows:(Lorg/testng/Assert$ThrowingRunnable;)V</name>
        <version>1.8</version>
        <comment>Use AssertJ's assertThatThrownBy, see https://github.com/trinodb/trino/issues/5320 for rationale</comment>
    </violation>

    <violation>
        <name>org/testng/Assert.assertThrows:(Ljava/lang/Class;Lorg/testng/Assert$ThrowingRunnable;)V</name>
        <version>1.8</version>
        <comment>Use AssertJ's assertThatThrownBy, see https://github.com/trinodb/trino/issues/5320 for rationale</comment>
    </violation>

    <violation>
        <name>org/apache/hadoop/conf/Configuration."&lt;init&gt;":()V</name>
        <version>1.1</version>
        <comment>Prefer new Configuration(false)</comment>
    </violation>

    <violation>
        <name>java/util/TimeZone.getTimeZone:(Ljava/lang/String;)Ljava/util/TimeZone;</name>
        <version>1.8</version>
        <comment>Avoid TimeZone.getTimeZone as it returns GMT for a zone not supported by the JVM. Use TimeZone.getTimeZone(ZoneId.of(..)) instead, or TimeZone.getTimeZone(..., false).</comment>
    </violation>

    <violation>
        <name>org/joda/time/DateTimeZone.toTimeZone:()Ljava/util/TimeZone;</name>
        <version>1.8</version>
        <comment>Avoid DateTimeZone.toTimeZone as it returns GMT for a zone not supported by the JVM. Use TimeZone.getTimeZone(ZoneId.of(dtz.getId())) instead.</comment>
    </violation>

    <violation>
        <name>com/esri/core/geometry/ogc/OGCGeometry.equals:(Lcom/esri/core/geometry/ogc/OGCGeometry;)Z</name>
        <version>1.6</version>
        <comment>Prefer OGCGeometry.Equals(OGCGeometry)</comment>
    </violation>

    <violation>
        <name>com/esri/core/geometry/ogc/OGCGeometry.equals:(Ljava/lang/Object;)Z</name>
        <version>1.6</version>
        <comment>Prefer OGCGeometry.Equals(OGCGeometry)</comment>
    </violation>

    <violation>
        <name>io/airlift/units/DataSize."&lt;init&gt;":(DLio/airlift/units/DataSize$Unit;)V</name>
        <version>1.8</version>
        <comment>Use io.airlift.units.DataSize.of(long, DataSize.Unit)</comment>
    </violation>

    <violation>
        <name>io/airlift/units/DataSize.succinctDataSize:(DLio/airlift/units/DataSize$Unit;)Lio/airlift/units/DataSize;</name>
        <version>1.8</version>
        <comment>Use io.airlift.units.DataSize.of(long, DataSize.Unit).succinct() -- Note that succinct conversion only affects toString() results</comment>
    </violation>

    <violation>
        <name>io/airlift/units/DataSize.getValue:()D</name>
        <version>1.8</version>
        <comment>Use io.airlift.units.DataSize.toBytes() and Unit.inBytes() for conversion</comment>
    </violation>

    <violation>
        <name>io/airlift/units/DataSize.getValue:(Lio/airlift/units/DataSize$Unit;)D</name>
        <version>1.8</version>
        <comment>Use io.airlift.units.DataSize.toBytes() and Unit.inBytes() for conversion</comment>
    </violation>

    <violation>
        <name>io/airlift/units/DataSize.roundTo:(Lio/airlift/units/DataSize$Unit;)J</name>
        <version>1.8</version>
        <comment>Method is deprecated for removal</comment>
    </violation>

    <violation>
        <name>io/airlift/units/DataSize.convertTo:(Lio/airlift/units/DataSize$Unit;)Lio/airlift/units/DataSize;</name>
        <version>1.8</version>
        <comment>Use io.airlift.units.DataSize.to(DataSize.Unit)</comment>
    </violation>

    <violation>
        <name>io/airlift/units/DataSize.convertToMostSuccinctDataSize:()Lio/airlift/units/DataSize;</name>
        <version>1.8</version>
        <comment>Use io.airlift.units.DataSize.succinct()</comment>
    </violation>

    <violation>
        <name>io/airlift/testing/Closeables.closeQuietly:([Ljava/io/Closeable;)V</name>
        <version>1.0</version>
        <comment>Use Closeables.closeAll() or Closer.</comment>
    </violation>

    <violation>
        <name>com/google/inject/util/Modules.combine:(Ljava/lang/Iterable;)Lcom/google/inject/Module;</name>
        <version>1.8</version>
        <comment>Use io.airlift.configuration.ConfigurationAwareModule.combine</comment>
    </violation>

    <violation>
        <name>com/google/inject/util/Modules.combine:([Lcom/google/inject/Module;)Lcom/google/inject/Module;</name>
        <version>1.8</version>
        <comment>Use io.airlift.configuration.ConfigurationAwareModule.combine</comment>
    </violation>
</modernizer>
