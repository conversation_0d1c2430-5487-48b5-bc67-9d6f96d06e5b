[package]
name = "wren-sqllogictest"
authors.workspace = true
edition.workspace = true
homepage.workspace = true
license.workspace = true
readme.workspace = true
repository.workspace = true
rust-version.workspace = true
version.workspace = true

[lib]
name = "wren_sqllogictest"
path = "src/lib.rs"

[dependencies]
async-trait = "0.1.80"
bigdecimal = "0.4.3"
datafusion = { workspace = true, default-features = true }
half = { version = "2.4.1", default-features = true }
log = { workspace = true }
rust_decimal = { version = "1.27.0" }
sqllogictest = "0.26.4"
thiserror = "2.0.3"
tokio = { workspace = true }
wren-core = { path = "../core" }

itertools = "0.14.0"
object_store = { version = "0.11.0", default-features = false }

clap = { version = "4.4.8", features = ["derive", "env"] }
futures = "0.3.17"
tempfile = "3.10.1"

[dev-dependencies]
env_logger = "0.11.3"
num_cpus = "1.16.0"
tokio = { workspace = true, features = ["rt-multi-thread"] }

[[test]]
harness = false
name = "sqllogictests"
path = "bin/sqllogictests.rs"
