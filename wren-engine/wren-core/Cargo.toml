[workspace]
members = ["benchmarks", "core", "sqllogictest", "wren-example"]
resolver = "2"

[workspace.package]
authors = ["Canner <<EMAIL>>"]
edition = "2021"
homepage = "https://getwren.ai"
license = "Apache-2.0"
readme = "README.md"
repository = "https://https://github.com/Canner/wren-engine"
rust-version = "1.78"
version = "0.1.0"

[workspace.dependencies]
async-trait = "0.1.88"
datafusion = { git = "https://github.com/Canner/datafusion.git", branch = "v46.0.1" }
env_logger = "0.11.3"
hashbrown = "0.15.2"
insta = { version = "1.41.1" }
log = { version = "0.4.14" }
serde = { version = "1.0.201", features = ["derive", "rc"] }
serde_json = { version = "1.0.117" }
serde_with = { version = "3.11.0" }
tokio = { version = "1.4.0", features = ["rt", "rt-multi-thread", "macros"] }
wren-core = { path = "core" }
wren-core-base = { path = "../wren-core-base" }
