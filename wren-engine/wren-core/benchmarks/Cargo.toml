[package]
name = "wren-benchmarks"
authors.workspace = true
edition.workspace = true
homepage.workspace = true
license.workspace = true
readme.workspace = true
repository.workspace = true
rust-version.workspace = true
version.workspace = true

[lib]
name = "wren_benchmarks"
path = "src/lib.rs"

[dependencies]
datafusion = { workspace = true }
env_logger = { workspace = true }
log = "0.4.21"
num_cpus = "1.16.0"
serde = { workspace = true }
serde_json = { workspace = true }
structopt = { version = "0.3.26", default-features = false }
tokio = { workspace = true }
wren-core = { workspace = true }
