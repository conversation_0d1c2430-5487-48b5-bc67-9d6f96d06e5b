[package]
name = "wren-core"
include = ["src/**/*.rs", "Cargo.toml"]
version = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }
license = { workspace = true }
authors = { workspace = true }

[lib]
name = "wren_core"
path = "src/lib.rs"

[dependencies]
async-trait = { workspace = true }
csv = "1.3.0"
datafusion = { workspace = true, features = [
    "nested_expressions",
    "crypto_expressions",
    "datetime_expressions",
    "encoding_expressions",
    "regex_expressions",
    "unicode_expressions",
] }
env_logger = { workspace = true }
log = { workspace = true }
parking_lot = "0.12.3"
petgraph = "0.7.1"
petgraph-evcxr = "*"
regex = "1.10.5"
serde = { workspace = true }
serde_json = { workspace = true }
serde_with = { workspace = true }
tokio = { workspace = true, features = ["rt", "rt-multi-thread", "macros"] }
wren-core-base = { workspace = true }

[dev-dependencies]
insta = { workspace = true }
