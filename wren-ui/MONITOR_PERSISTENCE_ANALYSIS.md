# Monitor Persistence Analysis & Implementation Plan

## Executive Summary

The current monitor functionality provides excellent real-time interaction but lacks database persistence. This analysis outlines the current architecture, identifies gaps, and provides a comprehensive plan to implement full monitor persistence following WrenAI's existing patterns. **This is a rewrite - backward compatibility is not required.**

## Current Architecture Analysis

### 1. Data Flow Patterns

**Existing Ask Flow (Reference Pattern):**
```
User Question → AnswerResult.tsx → usePromptThreadStore → GraphQL Mutation → 
AskingResolver → AskingService → WrenAI API → Background Tracking → 
Database Updates (thread_response table)
```

**Current Monitor Flow (Missing Persistence):**
```
User Trigger → MonitorAnswer.tsx → useMonitorGeneration → GraphQL Mutation → 
MonitorResolver → WrenAI API → Background Tracking → In-Memory State Only
```

### 2. Database Schema Current State

**Core Tables:**
- `thread` - Main conversation threads
- `thread_response` - Individual responses within threads (our integration point)
- `asking_task` - Background task tracking
- `view` - Saved SQL views
- `sql_pair` - Knowledge base of question-SQL pairs

**ThreadResponse Schema:**
```sql
CREATE TABLE thread_response (
  id SERIAL PRIMARY KEY,
  thread_id INTEGER REFERENCES thread(id),
  question TEXT,
  sql TEXT,
  answer_detail JSONB,
  breakdown_detail JSONB,
  chart_detail JSONB,
  view_id INTEGER REFERENCES view(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 3. Current Monitor State Management

**Component State (Non-persistent):**
- `useMonitorGeneration` - Monitor generation state
- `useMonitorChat` - Chat refinement state
- `MonitorGeneration[]` - Array of generations stored in component state
- Task tracking via `monitorTaskIds` in global store

**Data Structures:**
```typescript
interface MonitorGeneration {
  id: string;
  result: MonitorGenerationResult;
  timestamp: Date;
  chatPrompt?: string;
}

interface MonitorGenerationResult {
  monitor_sql?: string;
  reasoning_steps?: string[];
  expression_suggestions?: any[];
  validation_results?: any;
}
```

### 4. GraphQL Operations (Current)

```graphql
# Monitor Generation
mutation GenerateMonitor($data: GenerateMonitorInput!) {
  generateMonitor(data: $data) { id }
}

query GetMonitorGenerationResult($taskId: String!) {
  getMonitorGenerationResult(taskId: $taskId) {
    status
    response { monitor_sql, reasoning_steps, validation_results }
    error { code, message }
  }
}

# Chat Refinement
mutation ChatWithMonitor($data: ChatWithMonitorInput!) {
  chatWithMonitor(data: $data) { id }
}
```

## Gap Analysis

### 1. Critical Missing Features

**Database Persistence:**
- ❌ Monitor generations are not saved to database
- ❌ Chat refinements are lost on page refresh
- ❌ No monitor versioning or history
- ❌ No monitor metadata (creation date, user, etc.)

**Data Recovery:**
- ❌ Cannot recover monitor state after browser refresh
- ❌ Cannot resume monitor generation after page reload
- ❌ No audit trail for monitor evolution

**Chat Optimization:**
- ❌ Chat sends full state instead of incremental updates
- ❌ No chat history persistence
- ❌ No parent-child relationship tracking

### 2. Integration Challenges

**Thread Response Integration:**
- Need to link monitors to thread responses
- Need to handle monitor-specific data separately from ask data
- **No backward compatibility required - this is a rewrite**

**State Management:**
- Need to sync database state with component state
- Need to handle optimistic updates
- Need to manage loading states during persistence

## Database Schema (Option 1: Dedicated Monitor Tables)

```sql
-- Monitor generations table
CREATE TABLE monitor_generation (
  id SERIAL PRIMARY KEY,
  thread_response_id INTEGER NOT NULL REFERENCES thread_response(id) ON DELETE CASCADE,
  task_id VARCHAR(255) UNIQUE,
  monitor_sql TEXT,
  reasoning_steps JSONB,
  validation_results JSONB,
  expression_suggestions JSONB,      -- RENAMED: from template_suggestions
  monitor_expression TEXT,           -- User-defined expressions
  expression_result BOOLEAN,         -- Last expression result
  status VARCHAR(50) DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Monitor chat refinements table
CREATE TABLE monitor_chat_refinement (
  id SERIAL PRIMARY KEY,
  monitor_generation_id INTEGER NOT NULL REFERENCES monitor_generation(id) ON DELETE CASCADE,
  parent_refinement_id INTEGER REFERENCES monitor_chat_refinement(id) ON DELETE CASCADE,
  chat_prompt TEXT NOT NULL,
  refined_sql TEXT,
  reasoning_steps JSONB,
  validation_results JSONB,
  task_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_monitor_generation_thread_response ON monitor_generation(thread_response_id);
CREATE INDEX idx_monitor_generation_task_id ON monitor_generation(task_id);
CREATE INDEX idx_monitor_generation_status ON monitor_generation(status);
CREATE INDEX idx_monitor_chat_refinement_monitor ON monitor_chat_refinement(monitor_generation_id);
CREATE INDEX idx_monitor_chat_refinement_parent ON monitor_chat_refinement(parent_refinement_id);
CREATE INDEX idx_monitor_chat_refinement_task_id ON monitor_chat_refinement(task_id);
```

## Implementation Plan

### Phase 1: Database Schema & Migrations

#### Tasks:
1. **Create Migration Files**
   - Create `monitor_generation` table migration
   - Create `monitor_chat_refinement` table migration
   - Add indexes for performance
   - Test migration up/down

2. **Update Database Models**
   - Create `MonitorGeneration` model
   - Create `MonitorChatRefinement` model
   - Update relationships with `ThreadResponse`
   - Add validation rules

3. **Database Repository Layer**
   - Create `MonitorGenerationRepository`
   - Create `MonitorChatRefinementRepository`
   - Add CRUD operations
   - Add relationship queries

### Phase 2: GraphQL Schema & Resolvers

#### Tasks:
1. **Update GraphQL Schema**
   ```graphql
   type MonitorGeneration {
     id: ID!
     threadResponseId: ID!
     taskId: String!
     monitorSql: String
     reasoningSteps: [String!]
     validationResults: JSON
     expressionSuggestions: JSON
     monitorExpression: String
     expressionResult: Boolean
     status: String!
     chatRefinements: [MonitorChatRefinement!]!
     createdAt: DateTime!
     updatedAt: DateTime!
   }

   type MonitorChatRefinement {
     id: ID!
     monitorGenerationId: ID!
     parentRefinementId: ID
     chatPrompt: String!
     refinedSql: String
     reasoningSteps: [String!]
     validationResults: JSON
     taskId: String
     createdAt: DateTime!
   }

   extend type Mutation {
     persistMonitorGeneration(data: PersistMonitorGenerationInput!): MonitorGeneration!
     persistChatRefinement(data: PersistChatRefinementInput!): MonitorChatRefinement!
     updateMonitorExpression(id: ID!, expression: String!, result: Boolean): MonitorGeneration!
   }

   extend type Query {
     getMonitorGenerations(threadResponseId: ID!): [MonitorGeneration!]!
     getMonitorChatHistory(monitorGenerationId: ID!): [MonitorChatRefinement!]!
   }
   ```

2. **Create Resolvers**
   - `MonitorGenerationResolver`
   - `MonitorChatRefinementResolver`
   - Update existing resolvers to include monitor data

3. **Update Existing Resolvers**
   - Modify `ThreadResponseResolver` to include monitor data
   - Update `generateMonitor` mutation to persist results
   - Update `chatWithMonitor` mutation to persist chat

### Phase 3: Backend Service Layer

#### Tasks:
1. **Create Monitor Service**
   - `MonitorGenerationService`
   - Handle monitor lifecycle management
   - Integrate with existing `AskingService` patterns

2. **Update Existing Services**
   - Modify `AskingService` to handle monitor persistence
   - Update task tracking to include monitor tasks
   - Add monitor cleanup for failed tasks

3. **Background Task Integration**
   - Update monitor task handlers to persist results
   - Add retry logic for failed persistence
   - Add cleanup for orphaned monitor tasks

### Phase 4: Frontend State Management

#### Tasks:
1. **Update Store Interface**
   ```typescript
   interface IPromptThreadStore {
     // Existing fields...
     monitorGenerations?: Record<number, MonitorGeneration[]>;
     
     // New methods
     persistMonitorGeneration?: (threadResponseId: number, monitor: MonitorGeneration) => Promise<void>;
     persistChatRefinement?: (monitorId: string, chat: MonitorChatRefinement) => Promise<void>;
     updateMonitorExpression?: (monitorId: string, expression: string, result: boolean) => Promise<void>;
     loadMonitorGenerations?: (threadResponseId: number) => Promise<void>;
   }
   ```

2. **Update Hooks**
   - Modify `useMonitorGeneration` to handle persistence
   - Update `useMonitorChat` to use incremental updates
   - Add optimistic updates for better UX

3. **Update Components**
   - Modify `MonitorAnswerContainer` to load persisted data
   - Update `MonitorAnswerInstance` to handle expression persistence
   - Add loading states for database operations

### Phase 5: GraphQL Integration

#### Tasks:
1. **Create GraphQL Operations**
   ```typescript
   // Generated GraphQL operations
   const PERSIST_MONITOR_GENERATION = gql`
     mutation PersistMonitorGeneration($data: PersistMonitorGenerationInput!) {
       persistMonitorGeneration(data: $data) {
         id
         threadResponseId
         taskId
         monitorSql
         reasoningSteps
         validationResults
         expressionSuggestions
         monitorExpression
         expressionResult
         status
         createdAt
         updatedAt
       }
     }
   `;

   const PERSIST_CHAT_REFINEMENT = gql`
     mutation PersistChatRefinement($data: PersistChatRefinementInput!) {
       persistChatRefinement(data: $data) {
         id
         monitorGenerationId
         parentRefinementId
         chatPrompt
         refinedSql
         reasoningSteps
         validationResults
         taskId
         createdAt
       }
     }
   `;

   const GET_MONITOR_GENERATIONS = gql`
     query GetMonitorGenerations($threadResponseId: ID!) {
       getMonitorGenerations(threadResponseId: $threadResponseId) {
         id
         taskId
         monitorSql
         reasoningSteps
         validationResults
         expressionSuggestions
         monitorExpression
         expressionResult
         status
         chatRefinements {
           id
           chatPrompt
           refinedSql
           reasoningSteps
           validationResults
           createdAt
         }
         createdAt
         updatedAt
       }
     }
   `;
   ```

2. **Update Existing Operations**
   - Modify existing monitor mutations to include persistence
   - Add monitor data to thread response queries
   - Update error handling for persistence failures

### Phase 6: Expression Persistence Integration

#### Tasks:
1. **Database Integration**
   - Add expression fields to monitor generation table
   - Create mutation to update expressions
   - Add validation for expression syntax

2. **Frontend Integration**
   - Update `SelectablePreviewData` to persist expressions
   - Add debouncing for expression updates
   - Handle expression validation on server

3. **Real-time Updates**
   - Add optimistic updates for expressions
   - Handle expression evaluation results
   - Sync expression state across components

### Phase 7: Chat Optimization

#### Tasks:
1. **Incremental Chat Updates**
   - Modify chat flow to send only new messages
   - Add parent-child relationship tracking
   - Implement chat history loading

2. **State Synchronization**
   - Sync chat state with database
   - Handle offline/online state transitions
   - Add conflict resolution for concurrent edits

3. **Performance Optimization**
   - Implement pagination for chat history
   - Add caching for frequently accessed chat data
   - Optimize query performance

### Phase 8: Testing & Validation

#### Tasks:
1. **Unit Tests**
   - Test database models and repositories
   - Test GraphQL resolvers
   - Test frontend state management

2. **Integration Tests**
   - Test complete monitor flow with persistence
   - Test chat refinement persistence
   - Test expression persistence

3. **E2E Tests**
   - Test monitor generation and persistence
   - Test chat refinement flow
   - Test expression builder integration

## Database Migration Strategy

### Migration 1: Core Tables
```sql
-- 001_create_monitor_generation.sql
CREATE TABLE monitor_generation (
  id SERIAL PRIMARY KEY,
  thread_response_id INTEGER NOT NULL REFERENCES thread_response(id) ON DELETE CASCADE,
  task_id VARCHAR(255) UNIQUE,
  monitor_sql TEXT,
  reasoning_steps JSONB,
  validation_results JSONB,
  expression_suggestions JSONB,
  monitor_expression TEXT,
  expression_result BOOLEAN,
  status VARCHAR(50) DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_monitor_generation_thread_response ON monitor_generation(thread_response_id);
CREATE INDEX idx_monitor_generation_task_id ON monitor_generation(task_id);
CREATE INDEX idx_monitor_generation_status ON monitor_generation(status);
```

### Migration 2: Chat Refinements
```sql
-- 002_create_monitor_chat_refinement.sql
CREATE TABLE monitor_chat_refinement (
  id SERIAL PRIMARY KEY,
  monitor_generation_id INTEGER NOT NULL REFERENCES monitor_generation(id) ON DELETE CASCADE,
  parent_refinement_id INTEGER REFERENCES monitor_chat_refinement(id) ON DELETE CASCADE,
  chat_prompt TEXT NOT NULL,
  refined_sql TEXT,
  reasoning_steps JSONB,
  validation_results JSONB,
  task_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_monitor_chat_refinement_monitor ON monitor_chat_refinement(monitor_generation_id);
CREATE INDEX idx_monitor_chat_refinement_parent ON monitor_chat_refinement(parent_refinement_id);
CREATE INDEX idx_monitor_chat_refinement_task_id ON monitor_chat_refinement(task_id);
```

## Risk Assessment

### High Risk:
- **Data Migration:** We don't need to be concerned about this as we are not migrating existing data
- **State Synchronization:** Complex sync between database and component state
- **Performance Impact:** Additional database queries may slow down monitor flow

### Medium Risk:
- **GraphQL Schema Changes:** May break existing client code. We will just fix it since it is a rewrite
- **Chat Flow Changes:** Current chat implementation may need significant refactoring

### Low Risk:
- **Database Schema:** Well-defined schema with proper relationships
- **Expression Persistence:** Isolated feature with minimal impact
- **Testing:** Comprehensive testing strategy covers most scenarios

## Success Metrics

1. **Functionality:**
   - All monitor generations are persisted to database
   - Chat refinements are saved and loadable
   - Expressions persist across page refreshes
   - Monitor state recovers after browser reload

2. **Performance:**
   - Monitor generation time increase < 200ms
   - Chat refinement time increase < 100ms
   - Page load time increase < 500ms for existing monitors

3. **User Experience:**
   - No data loss during monitor interactions
   - Seamless chat experience with history
   - Expression builder works consistently
   - Monitor versioning provides clear history

## Conclusion

This implementation plan provides a comprehensive approach to adding full persistence to the monitor functionality. The plan follows WrenAI's existing patterns and provides a solid foundation for future monitor features like scheduling, alerting, and dashboard integration.

**Key Changes from Original Requirements:**
- **No backward compatibility required** - this is a complete rewrite
- **Using dedicated monitor tables** (Option 1) for better data organization
- **Renamed `template_suggestions` to `expression_suggestions`** to better reflect functionality
- **Complete chat flow rewrite** for proper persistence and optimization

The phased approach allows for iterative development and testing, reducing risk while ensuring all features are properly integrated into the existing codebase. Since this is a rewrite, we can optimize the architecture without worrying about maintaining existing functionality.