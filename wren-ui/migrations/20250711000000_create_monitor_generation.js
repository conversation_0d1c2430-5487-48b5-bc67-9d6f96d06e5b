/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.createTable('monitor_generation', (table) => {
    table.increments('id').primary();
    table
      .integer('thread_response_id')
      .notNullable()
      .references('id')
      .inTable('thread_response')
      .onDelete('CASCADE');
    table.string('task_id').unique();
    table.text('monitor_sql');
    table.jsonb('reasoning_steps');
    table.jsonb('validation_results');
    table.jsonb('expression_suggestions');
    table.text('monitor_expression');
    table.boolean('expression_result');
    table
      .enum('status', [
        'UNDERSTANDING',
        'REASONING',
        'GENERATING',
        'VALIDATING',
        'FINISHED',
        'FAILED',
        'STOPPED',
        'PENDING',
      ])
      .defaultTo('PENDING');
    table.jsonb('expression_builder_state');
    table.jsonb('key_insights');
    table.jsonb('ui_state');
    table.timestamps(true, true);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.dropTable('monitor_generation');
};
