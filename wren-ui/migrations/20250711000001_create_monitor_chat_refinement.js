/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.createTable('monitor_chat_refinement', (table) => {
    table.increments('id').primary();
    table
      .integer('monitor_generation_id')
      .notNullable()
      .references('id')
      .inTable('monitor_generation')
      .onDelete('CASCADE');
    table
      .integer('parent_refinement_id')
      .references('id')
      .inTable('monitor_chat_refinement')
      .onDelete('CASCADE');
    table.text('chat_prompt').notNullable();
    table.text('refined_sql');
    table.jsonb('reasoning_steps');
    table.jsonb('validation_results');
    table.string('task_id');
    table
      .enum('status', [
        'UNDERSTANDING',
        'REASONING',
        'GENERATING',
        'VALIDATING',
        'FINISHED',
        'FAILED',
        'STOPPED',
        'PENDING',
      ])
      .defaultTo('PENDING');
    table.jsonb('key_insights');
    table.timestamps(true, true);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.dropTable('monitor_chat_refinement');
};
