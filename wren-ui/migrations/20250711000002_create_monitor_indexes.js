/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema
    .alterTable('monitor_generation', (table) => {
      // Add indexes for performance
      table.index(
        'thread_response_id',
        'idx_monitor_generation_thread_response',
      );
      table.index('task_id', 'idx_monitor_generation_task_id');
      table.index('status', 'idx_monitor_generation_status');
    })
    .alterTable('monitor_chat_refinement', (table) => {
      // Add indexes for performance
      table.index(
        'monitor_generation_id',
        'idx_monitor_chat_refinement_monitor',
      );
      table.index('parent_refinement_id', 'idx_monitor_chat_refinement_parent');
      table.index('task_id', 'idx_monitor_chat_refinement_task_id');
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema
    .alterTable('monitor_generation', (table) => {
      table.dropIndex(
        'thread_response_id',
        'idx_monitor_generation_thread_response',
      );
      table.dropIndex('task_id', 'idx_monitor_generation_task_id');
      table.dropIndex('status', 'idx_monitor_generation_status');
    })
    .alterTable('monitor_chat_refinement', (table) => {
      table.dropIndex(
        'monitor_generation_id',
        'idx_monitor_chat_refinement_monitor',
      );
      table.dropIndex(
        'parent_refinement_id',
        'idx_monitor_chat_refinement_parent',
      );
      table.dropIndex('task_id', 'idx_monitor_chat_refinement_task_id');
    });
};
