import { useCallback, useEffect, useMemo, useState } from 'react';
import { message } from 'antd';
import {
  MonitorGenerationStatus,
  GenerateMonitorInput,
  CreateMonitorGenerationInput,
  UpdateMonitorGenerationInput,
  MonitorGeneration,
} from '@/apollo/client/graphql/__types__';
import {
  useGenerateMonitorMutation,
  useGetMonitorGenerationResultLazyQuery,
  useCancelMonitorGenerationMutation,
} from '@/apollo/client/graphql/monitors.generated';
import {
  useCreateMonitorGenerationMutation,
  useUpdateMonitorGenerationMutation,
  useUpdateMonitorExpressionMutation,
  useDeleteMonitorGenerationMutation,
  useMonitorGenerationsByThreadResponseQuery,
  useMonitorGenerationLazyQuery,
} from '@/apollo/client/graphql/monitorPersistence.generated';

export interface UseMonitorGenerationPersistenceReturn {
  // Generation state
  isGenerating: boolean;
  generationStatus: MonitorGenerationStatus | null;
  generationResult: any | null;
  generationError: any | null;

  // Persistence state
  persistedGenerations: MonitorGeneration[];
  loadingPersisted: boolean;
  persistenceError: any | null;

  // Loading states for different operations
  loadingStates: {
    creating: boolean;
    updating: boolean;
    deleting: boolean;
    loadingList: boolean;
    updatingExpression: boolean;
    cancelling: boolean;
  };

  // Actions
  generateMonitor: (
    input: GenerateMonitorInput & { threadResponseId: number },
  ) => Promise<string | null>;
  cancelGeneration: (taskId: string) => Promise<boolean>;

  // Persistence actions
  loadPersistedGenerations: (threadResponseId: number) => Promise<void>;
  persistGeneration: (
    data: CreateMonitorGenerationInput,
  ) => Promise<MonitorGeneration | null>;
  updateGeneration: (
    data: UpdateMonitorGenerationInput,
  ) => Promise<MonitorGeneration | null>;
  updateExpression: (
    id: number,
    expression: string,
    result: boolean,
    builderState?: any,
  ) => Promise<void>;
  deleteGeneration: (id: number) => Promise<boolean>;

  // Utilities
  resetGeneration: () => void;
  getGenerationById: (id: number) => Promise<MonitorGeneration | null>;
}

export default function useMonitorGenerationPersistence(): UseMonitorGenerationPersistenceReturn {
  // Generation state (existing)
  const [_currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  const [generationStatus, setGenerationStatus] =
    useState<MonitorGenerationStatus | null>(null);
  const [generationResult, setGenerationResult] = useState<any | null>(null);
  const [generationError, setGenerationError] = useState<any | null>(null);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(
    null,
  );

  // Persistence state (new)
  const [persistedGenerations, setPersistedGenerations] = useState<
    MonitorGeneration[]
  >([]);
  const [persistenceError, setPersistenceError] = useState<any | null>(null);
  const [currentGenerationId, setCurrentGenerationId] = useState<number | null>(
    null,
  );

  // Loading states for different operations
  const [loadingStates, setLoadingStates] = useState({
    creating: false,
    updating: false,
    deleting: false,
    loadingList: false,
    updatingExpression: false,
    cancelling: false,
  });

  // Helper function to update loading state
  const updateLoadingState = useCallback(
    (operation: keyof typeof loadingStates, isLoading: boolean) => {
      setLoadingStates((prev) => ({ ...prev, [operation]: isLoading }));
    },
    [],
  );

  // Original mutations
  const [generateMonitorMutation, { loading: generateLoading }] =
    useGenerateMonitorMutation({
      onCompleted: (data) => {
        const taskId = data.generateMonitor.id;
        setCurrentTaskId(taskId);
        setGenerationStatus(MonitorGenerationStatus.UNDERSTANDING);
        startPolling(taskId);
        message.success('Monitor generation started');
      },
      onError: (error) => {
        console.error('Generate monitor error:', error);
        message.error(`Failed to start monitor generation: ${error.message}`);
        setGenerationError(error);
      },
    });

  const [fetchGenerationResult] = useGetMonitorGenerationResultLazyQuery({
    errorPolicy: 'all',
    onCompleted: async (data) => {
      const result = data.getMonitorGenerationResult;
      setGenerationStatus(result.status as MonitorGenerationStatus);

      // Persist the result when generation completes
      if (
        result.status === MonitorGenerationStatus.FINISHED &&
        result.response &&
        currentGenerationId
      ) {
        try {
          await updateMonitorGenerationMutation({
            variables: {
              data: {
                id: currentGenerationId,
                monitorSql: result.response.monitor_sql || undefined,
                reasoningSteps: result.response.reasoning_steps || undefined,
                validationResults:
                  result.response.validation_results || undefined,
                expressionSuggestions:
                  result.response.template_suggestions || undefined,
                status: MonitorGenerationStatus.FINISHED,
              },
            },
          });
        } catch (error) {
          console.error('Failed to persist completed generation:', error);
        }
      }

      if (result.status === MonitorGenerationStatus.FINISHED) {
        setGenerationResult(result.response);
        stopPolling();
        message.success('Monitor generation completed successfully!');
      } else if (result.status === MonitorGenerationStatus.FAILED) {
        setGenerationError(result.error);
        stopPolling();
        message.error('Monitor generation failed');

        // Update persistence with failed status
        if (currentGenerationId) {
          try {
            await updateMonitorGenerationMutation({
              variables: {
                data: {
                  id: currentGenerationId,
                  status: MonitorGenerationStatus.FAILED,
                },
              },
            });
          } catch (error) {
            console.error('Failed to persist failed generation:', error);
          }
        }
      }
    },
    onError: (error) => {
      console.error('Fetch generation result error:', error);
      setGenerationError(error);
      stopPolling();
    },
  });

  const [cancelGenerationMutation] = useCancelMonitorGenerationMutation({
    onCompleted: async () => {
      stopPolling();
      setGenerationStatus(MonitorGenerationStatus.STOPPED);
      message.info('Monitor generation cancelled');

      // Update persistence with stopped status
      if (currentGenerationId) {
        try {
          await updateMonitorGenerationMutation({
            variables: {
              data: {
                id: currentGenerationId,
                status: MonitorGenerationStatus.STOPPED,
              },
            },
          });
        } catch (error) {
          console.error('Failed to persist cancelled generation:', error);
        }
      }
    },
    onError: (error) => {
      console.error('Cancel generation error:', error);
      message.error(`Failed to cancel generation: ${error.message}`);
    },
  });

  // Persistence mutations
  const [createMonitorGenerationMutation] = useCreateMonitorGenerationMutation({
    onError: (error) => {
      console.error('Create monitor generation error:', error);
      setPersistenceError(error);
    },
  });

  const [updateMonitorGenerationMutation] = useUpdateMonitorGenerationMutation({
    onError: (error) => {
      console.error('Update monitor generation error:', error);
      setPersistenceError(error);
    },
  });

  const [updateMonitorExpressionMutation] = useUpdateMonitorExpressionMutation({
    onError: (error) => {
      console.error('Update monitor expression error:', error);
      setPersistenceError(error);
    },
  });

  const [deleteMonitorGenerationMutation] = useDeleteMonitorGenerationMutation({
    onError: (error) => {
      console.error('Delete monitor generation error:', error);
      setPersistenceError(error);
    },
  });

  // Persistence queries
  const {
    data: persistedData,
    loading: loadingPersisted,
    error: loadPersistedError,
    refetch: refetchPersisted,
  } = useMonitorGenerationsByThreadResponseQuery({
    skip: true,
    onError: (error) => {
      console.error('Load persisted generations error:', error);
      setPersistenceError(error);
    },
  });

  const [getMonitorGenerationById] = useMonitorGenerationLazyQuery({
    onError: (error) => {
      console.error('Get monitor generation by id error:', error);
      setPersistenceError(error);
    },
  });

  // Update persisted generations when data changes
  useEffect(() => {
    console.log(
      'useMonitorGenerationPersistence: persistedData changed:',
      persistedData,
    );
    if (persistedData?.monitorGenerationsByThreadResponse) {
      console.log(
        'useMonitorGenerationPersistence: Setting persisted generations:',
        persistedData.monitorGenerationsByThreadResponse,
      );
      setPersistedGenerations(persistedData.monitorGenerationsByThreadResponse);
    }
  }, [persistedData]);

  // Update persistence error when load error occurs
  useEffect(() => {
    if (loadPersistedError) {
      setPersistenceError(loadPersistedError);
    }
  }, [loadPersistedError]);

  // Computed values
  const isGenerating = useMemo(() => {
    return (
      generateLoading ||
      (generationStatus !== null &&
        generationStatus !== MonitorGenerationStatus.FINISHED &&
        generationStatus !== MonitorGenerationStatus.FAILED &&
        generationStatus !== MonitorGenerationStatus.STOPPED)
    );
  }, [generateLoading, generationStatus]);

  // Polling management
  const startPolling = useCallback(
    (taskId: string) => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }

      const interval = setInterval(() => {
        fetchGenerationResult({
          variables: { taskId },
        });
      }, 2000); // Poll every 2 seconds

      setPollingInterval(interval);
    },
    [fetchGenerationResult, pollingInterval],
  );

  const stopPolling = useCallback(() => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }
  }, [pollingInterval]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);

  // Actions
  const generateMonitor = useCallback(
    async (
      input: GenerateMonitorInput & { threadResponseId: number },
    ): Promise<string | null> => {
      try {
        // Reset previous state
        setGenerationResult(null);
        setGenerationError(null);
        setGenerationStatus(null);
        setPersistenceError(null);

        // OPTIMISTIC UPDATE: Add immediate placeholder for better UX
        const optimisticId = Date.now();
        const optimisticGeneration: MonitorGeneration = {
          id: optimisticId,
          threadResponseId: input.threadResponseId,
          taskId: null,
          monitorSql: null,
          reasoningSteps: null,
          validationResults: null,
          expressionSuggestions: null,
          monitorExpression: null,
          expressionResult: null,
          status: MonitorGenerationStatus.UNDERSTANDING,
          expressionBuilderState: null,
          keyInsights: null,
          uiState: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          chatRefinements: null,
        };

        setPersistedGenerations((prev) => [...prev, optimisticGeneration]);

        // Create persistence record first
        const persistResult = await createMonitorGenerationMutation({
          variables: {
            data: {
              threadResponseId: input.threadResponseId,
              status: MonitorGenerationStatus.PENDING,
            },
          },
        });

        if (!persistResult.data?.createMonitorGeneration) {
          // Remove optimistic update on failure
          setPersistedGenerations((prev) =>
            prev.filter((g) => g.id !== optimisticId),
          );
          throw new Error('Failed to create monitor generation record');
        }

        const generationId = persistResult.data.createMonitorGeneration.id;
        setCurrentGenerationId(generationId);

        // Replace optimistic update with actual database record
        setPersistedGenerations((prev) =>
          prev.map((g) =>
            g.id === optimisticId
              ? persistResult.data.createMonitorGeneration
              : g,
          ),
        );

        // Start the actual generation
        const result = await generateMonitorMutation({
          variables: { data: input },
        });

        // Update the database record with task ID when available
        if (result.data?.generateMonitor.id) {
          const taskId = result.data.generateMonitor.id;

          // Update local state with task ID
          setPersistedGenerations((prev) =>
            prev.map((g) =>
              g.id === generationId
                ? {
                    ...g,
                    taskId: taskId,
                    status: MonitorGenerationStatus.UNDERSTANDING,
                    updatedAt: new Date().toISOString(),
                  }
                : g,
            ),
          );
        }

        return result.data?.generateMonitor.id || null;
      } catch (error) {
        console.error('Generate monitor failed:', error);
        setGenerationError(error);

        // Remove optimistic update on error
        setPersistedGenerations((prev) =>
          prev.filter((g) => g.id === Date.now()),
        );
        return null;
      }
    },
    [generateMonitorMutation, createMonitorGenerationMutation],
  );

  const cancelGeneration = useCallback(
    async (taskId: string): Promise<boolean> => {
      try {
        updateLoadingState('cancelling', true);
        const result = await cancelGenerationMutation({
          variables: { taskId },
        });
        return result.data?.cancelMonitorGeneration || false;
      } catch (error) {
        console.error('Cancel generation failed:', error);
        return false;
      } finally {
        updateLoadingState('cancelling', false);
      }
    },
    [cancelGenerationMutation, updateLoadingState],
  );

  const loadPersistedGenerations = useCallback(
    async (threadResponseId: number): Promise<void> => {
      try {
        updateLoadingState('loadingList', true);
        setPersistenceError(null);
        await refetchPersisted({ where: { threadResponseId } });
      } catch (error) {
        console.error('Load persisted generations failed:', error);
        setPersistenceError(error);
      } finally {
        updateLoadingState('loadingList', false);
      }
    },
    [refetchPersisted, updateLoadingState],
  );

  const persistGeneration = useCallback(
    async (
      data: CreateMonitorGenerationInput,
    ): Promise<MonitorGeneration | null> => {
      try {
        updateLoadingState('creating', true);
        setPersistenceError(null);

        // OPTIMISTIC UPDATE: Add placeholder immediately for better UX
        const optimisticId = Date.now(); // Temporary ID
        const optimisticGeneration: MonitorGeneration = {
          id: optimisticId,
          threadResponseId: data.threadResponseId,
          taskId: data.taskId || null,
          monitorSql: data.monitorSql || null,
          reasoningSteps: data.reasoningSteps || null,
          validationResults: data.validationResults || null,
          expressionSuggestions: data.expressionSuggestions || null,
          monitorExpression: data.monitorExpression || null,
          expressionResult: data.expressionResult || null,
          status: data.status || MonitorGenerationStatus.PENDING,
          expressionBuilderState: data.expressionBuilderState || null,
          keyInsights: data.keyInsights || null,
          uiState: data.uiState || null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          chatRefinements: null,
        };

        setPersistedGenerations((prev) => [...prev, optimisticGeneration]);

        const result = await createMonitorGenerationMutation({
          variables: { data },
        });

        const generation = result.data?.createMonitorGeneration;
        if (generation) {
          // Replace optimistic update with real data
          setPersistedGenerations((prev) =>
            prev.map((g) => (g.id === optimisticId ? generation : g)),
          );
        } else {
          // Remove optimistic update if creation failed
          setPersistedGenerations((prev) =>
            prev.filter((g) => g.id !== optimisticId),
          );
        }

        return generation || null;
      } catch (error) {
        console.error('Persist generation failed:', error);
        setPersistenceError(error);

        // Remove optimistic update on error
        setPersistedGenerations((prev) =>
          prev.filter((g) => g.id !== Date.now()),
        );
        return null;
      } finally {
        updateLoadingState('creating', false);
      }
    },
    [createMonitorGenerationMutation, updateLoadingState],
  );

  const updateGeneration = useCallback(
    async (
      data: UpdateMonitorGenerationInput,
    ): Promise<MonitorGeneration | null> => {
      try {
        updateLoadingState('updating', true);
        setPersistenceError(null);
        const result = await updateMonitorGenerationMutation({
          variables: { data },
        });

        const generation = result.data?.updateMonitorGeneration;
        if (generation) {
          // Update local state optimistically
          setPersistedGenerations((prev) =>
            prev.map((g) => (g.id === generation.id ? generation : g)),
          );
        }

        return generation || null;
      } catch (error) {
        console.error('Update generation failed:', error);
        setPersistenceError(error);
        return null;
      } finally {
        updateLoadingState('updating', false);
      }
    },
    [updateMonitorGenerationMutation, updateLoadingState],
  );

  const updateExpression = useCallback(
    async (
      id: number,
      expression: string,
      result: boolean,
      builderState?: any,
    ): Promise<void> => {
      const currentState = [...persistedGenerations];
      try {
        updateLoadingState('updatingExpression', true);
        setPersistenceError(null);

        // OPTIMISTIC UPDATE: Update local state immediately for better UX
        setPersistedGenerations((prev) =>
          prev.map((g) =>
            g.id === id
              ? {
                  ...g,
                  monitorExpression: expression,
                  expressionResult: result,
                  expressionBuilderState:
                    builderState || g.expressionBuilderState,
                  updatedAt: new Date().toISOString(),
                }
              : g,
          ),
        );

        const updateResult = await updateMonitorExpressionMutation({
          variables: {
            id,
            expression,
            result,
            expressionBuilderState: builderState || null,
          },
        });

        // Update with server response or revert on error
        if (updateResult.data?.updateMonitorExpression) {
          const updated = updateResult.data.updateMonitorExpression;
          setPersistedGenerations((prev) =>
            prev.map((g) =>
              g.id === id
                ? {
                    ...g,
                    monitorExpression: updated.monitorExpression,
                    expressionResult: updated.expressionResult,
                    expressionBuilderState: updated.expressionBuilderState,
                    updatedAt: updated.updatedAt,
                  }
                : g,
            ),
          );
        }
      } catch (error) {
        console.error('Update expression failed:', error);
        setPersistenceError(error);

        // REVERT OPTIMISTIC UPDATE: Restore previous state on error
        setPersistedGenerations(currentState);
      } finally {
        updateLoadingState('updatingExpression', false);
      }
    },
    [updateMonitorExpressionMutation, persistedGenerations, updateLoadingState],
  );

  const deleteGeneration = useCallback(
    async (id: number): Promise<boolean> => {
      const currentState = [...persistedGenerations];
      try {
        updateLoadingState('deleting', true);
        setPersistenceError(null);

        // OPTIMISTIC UPDATE: Remove from local state immediately for better UX
        setPersistedGenerations((prev) => prev.filter((g) => g.id !== id));

        const result = await deleteMonitorGenerationMutation({
          variables: { where: { id } },
        });

        if (result.data?.deleteMonitorGeneration) {
          return true;
        } else {
          // Revert optimistic update if deletion failed
          setPersistedGenerations(currentState);
          return false;
        }
      } catch (error) {
        console.error('Delete generation failed:', error);
        setPersistenceError(error);

        // Revert optimistic update on error
        setPersistedGenerations(currentState);
        return false;
      } finally {
        updateLoadingState('deleting', false);
      }
    },
    [deleteMonitorGenerationMutation, persistedGenerations, updateLoadingState],
  );

  const getGenerationById = useCallback(
    async (id: number): Promise<MonitorGeneration | null> => {
      try {
        setPersistenceError(null);
        const result = await getMonitorGenerationById({
          variables: { id },
        });

        return result.data?.monitorGeneration || null;
      } catch (error) {
        console.error('Get generation by id failed:', error);
        setPersistenceError(error);
        return null;
      }
    },
    [getMonitorGenerationById],
  );

  const resetGeneration = useCallback(() => {
    stopPolling();
    setCurrentTaskId(null);
    setCurrentGenerationId(null);
    setGenerationStatus(null);
    setGenerationResult(null);
    setGenerationError(null);
    setPersistenceError(null);
  }, [stopPolling]);

  return {
    // Generation state
    isGenerating,
    generationStatus,
    generationResult,
    generationError,

    // Persistence state
    persistedGenerations,
    loadingPersisted,
    persistenceError,

    // Loading states for different operations
    loadingStates,

    // Actions
    generateMonitor,
    cancelGeneration,

    // Persistence actions
    loadPersistedGenerations,
    persistGeneration,
    updateGeneration,
    updateExpression,
    deleteGeneration,

    // Utilities
    resetGeneration,
    getGenerationById,
  };
}
