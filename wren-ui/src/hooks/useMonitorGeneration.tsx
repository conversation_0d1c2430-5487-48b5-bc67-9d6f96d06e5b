import { useCallback, useEffect, useMemo, useState } from 'react';
import { message } from 'antd';
import {
  MonitorGenerationStatus,
  GenerateMonitorInput,
} from '@/apollo/client/graphql/__types__';
import {
  useGenerateMonitorMutation,
  useGetMonitorGenerationResultLazyQuery,
  useCancelMonitorGenerationMutation,
} from '@/apollo/client/graphql/monitors.generated';

export interface UseMonitorGenerationReturn {
  // Generation state
  isGenerating: boolean;
  generationStatus: MonitorGenerationStatus | null;
  generationResult: any | null;
  generationError: any | null;

  // Actions
  generateMonitor: (input: GenerateMonitorInput) => Promise<string | null>;
  cancelGeneration: (taskId: string) => Promise<boolean>;

  // Utilities
  resetGeneration: () => void;
}

export default function useMonitorGeneration(): UseMonitorGenerationReturn {
  // State
  const [_currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  const [generationStatus, setGenerationStatus] =
    useState<MonitorGenerationStatus | null>(null);
  const [generationResult, setGenerationResult] = useState<any | null>(null);
  const [generationError, setGenerationError] = useState<any | null>(null);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(
    null,
  );

  // Mutations
  const [generateMonitorMutation, { loading: generateLoading }] =
    useGenerateMonitorMutation({
      onCompleted: (data) => {
        const taskId = data.generateMonitor.id;
        setCurrentTaskId(taskId);
        setGenerationStatus(MonitorGenerationStatus.UNDERSTANDING);
        startPolling(taskId);
        message.success('Monitor generation started');
      },
      onError: (error) => {
        console.error('Generate monitor error:', error);
        message.error(`Failed to start monitor generation: ${error.message}`);
        setGenerationError(error);
      },
    });

  const [fetchGenerationResult] = useGetMonitorGenerationResultLazyQuery({
    errorPolicy: 'all',
    onCompleted: (data) => {
      const result = data.getMonitorGenerationResult;
      setGenerationStatus(result.status as MonitorGenerationStatus);

      if (result.status === MonitorGenerationStatus.FINISHED) {
        setGenerationResult(result.response);
        stopPolling();
        message.success('Monitor generation completed successfully!');
      } else if (result.status === MonitorGenerationStatus.FAILED) {
        setGenerationError(result.error);
        stopPolling();
        message.error('Monitor generation failed');
      }
    },
    onError: (error) => {
      console.error('Fetch generation result error:', error);
      setGenerationError(error);
      stopPolling();
    },
  });

  const [cancelGenerationMutation] = useCancelMonitorGenerationMutation({
    onCompleted: () => {
      stopPolling();
      setGenerationStatus(MonitorGenerationStatus.STOPPED);
      message.info('Monitor generation cancelled');
    },
    onError: (error) => {
      console.error('Cancel generation error:', error);
      message.error(`Failed to cancel generation: ${error.message}`);
    },
  });

  // Computed values
  const isGenerating = useMemo(() => {
    return (
      generateLoading ||
      (generationStatus !== null &&
        generationStatus !== MonitorGenerationStatus.FINISHED &&
        generationStatus !== MonitorGenerationStatus.FAILED &&
        generationStatus !== MonitorGenerationStatus.STOPPED)
    );
  }, [generateLoading, generationStatus]);

  // Polling management
  const startPolling = useCallback(
    (taskId: string) => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }

      const interval = setInterval(() => {
        fetchGenerationResult({
          variables: { taskId },
        });
      }, 2000); // Poll every 2 seconds

      setPollingInterval(interval);
    },
    [fetchGenerationResult, pollingInterval],
  );

  const stopPolling = useCallback(() => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }
  }, [pollingInterval]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);

  // Actions
  const generateMonitor = useCallback(
    async (input: GenerateMonitorInput): Promise<string | null> => {
      try {
        // Reset previous state
        setGenerationResult(null);
        setGenerationError(null);
        setGenerationStatus(null);

        const result = await generateMonitorMutation({
          variables: { data: input },
        });

        return result.data?.generateMonitor.id || null;
      } catch (error) {
        console.error('Generate monitor failed:', error);
        return null;
      }
    },
    [generateMonitorMutation],
  );

  const cancelGeneration = useCallback(
    async (taskId: string): Promise<boolean> => {
      try {
        const result = await cancelGenerationMutation({
          variables: { taskId },
        });
        return result.data?.cancelMonitorGeneration || false;
      } catch (error) {
        console.error('Cancel generation failed:', error);
        return false;
      }
    },
    [cancelGenerationMutation],
  );

  const resetGeneration = useCallback(() => {
    stopPolling();
    setCurrentTaskId(null);
    setGenerationStatus(null);
    setGenerationResult(null);
    setGenerationError(null);
  }, [stopPolling]);

  return {
    // Generation state
    isGenerating,
    generationStatus,
    generationResult,
    generationError,

    // Actions
    generateMonitor,
    cancelGeneration,

    // Utilities
    resetGeneration,
  };
}
