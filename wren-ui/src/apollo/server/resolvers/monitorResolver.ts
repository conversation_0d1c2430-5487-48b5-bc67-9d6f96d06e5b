import { IContext } from '../types';
import { getLogger } from '@server/utils';
import { Manifest } from '@server/types/manifest';
import {
  MonitorGenerationInput,
  MonitorChatRefinementInput,
  UpdateMonitorGenerationInput,
  MonitorStatus,
} from '@server/models/monitor';

const logger = getLogger('MonitorResolver');
logger.level = 'debug';

export class MonitorResolver {
  constructor() {
    this.generateMonitor = this.generateMonitor.bind(this);
    this.getMonitorGenerationResult =
      this.getMonitorGenerationResult.bind(this);
    this.cancelMonitorGeneration = this.cancelMonitorGeneration.bind(this);
    this.chatWithMonitor = this.chatWithMonitor.bind(this);

    // Persistence methods
    this.createMonitorGeneration = this.createMonitorGeneration.bind(this);
    this.updateMonitorGeneration = this.updateMonitorGeneration.bind(this);
    this.deleteMonitorGeneration = this.deleteMonitorGeneration.bind(this);
    this.monitorGeneration = this.monitorGeneration.bind(this);
    this.monitorGenerationsByThreadResponse =
      this.monitorGenerationsByThreadResponse.bind(this);
    this.createMonitorChatRefinement =
      this.createMonitorChatRefinement.bind(this);
    this.monitorChatRefinements = this.monitorChatRefinements.bind(this);
    this.updateMonitorExpression = this.updateMonitorExpression.bind(this);
  }

  // Monitor Generation Methods (following ask flow patterns)
  public async generateMonitor(
    _root: any,
    args: {
      data: {
        threadResponseId: number;
        clarificationPrompt?: string;
      };
    },
    ctx: IContext,
  ): Promise<{ id: string }> {
    logger.debug(
      `Generating monitor for thread response ${args.data.threadResponseId}`,
    );

    const { threadResponseId, clarificationPrompt } = args.data;

    // Get thread response data (following existing patterns)
    const threadResponse =
      await ctx.askingService.getResponse(threadResponseId);

    if (!threadResponse) {
      throw new Error(`Thread response with id ${threadResponseId} not found`);
    }

    const project = await ctx.projectService.getCurrentProject();

    // Fetch schema information for the monitor generation (following ask flow patterns)
    logger.debug('Fetching schema information for monitor generation');
    let schemaContext = '';
    let deployId = '';
    try {
      // Get the deployed schema information (following ask pipeline patterns)
      const lastDeploy = await ctx.deployService.getLastDeployment(project.id);
      if (lastDeploy && lastDeploy.manifest) {
        deployId = lastDeploy.hash;

        // Extract relevant schema information from the manifest
        const manifest = lastDeploy.manifest as Manifest;
        if (manifest.models && manifest.models.length > 0) {
          // Build schema context from manifest models
          const schemaLines = manifest.models.map((model) => {
            const columns =
              model.columns
                ?.map(
                  (col) =>
                    `  ${col.name} ${col.type}${model.primaryKey === col.name ? ' PRIMARY KEY' : ''}`,
                )
                .join('\n') || '';

            return `Table: ${model.name}\n${columns}`;
          });

          schemaContext = schemaLines.join('\n\n');
          logger.debug(
            `Retrieved schema context from manifest: ${schemaContext.length} characters`,
          );
        }
      }
    } catch (schemaError: any) {
      logger.warn(`Failed to fetch schema information: ${schemaError.message}`);
      // Continue without schema context - monitor generation will work but with reduced accuracy
    }

    // Enhanced SQL reasoning with schema context
    const sqlReasoning = `User wants to analyze: "${threadResponse.question}". The SQL query was designed to extract relevant data patterns from the database. This query can be transformed into a monitoring system to track changes in the underlying data and alert when significant variations occur. The monitoring approach should focus on key metrics that would indicate business-relevant changes or anomalies in the data patterns.

Schema Context:
${schemaContext || 'No schema context available - using existing SQL structure for reference.'}`;

    // Prepare enhanced thread data (following existing patterns)
    const threadData = {
      answerDetail: threadResponse.answerDetail || null,
      chartDetail: threadResponse.chartDetail || null,
      breakdownDetail: threadResponse.breakdownDetail || null,
      schemaContext: schemaContext || null, // Add schema context to thread data
    };

    try {
      const taskId = await ctx.wrenAIAdaptor.generateMonitor({
        originalMessage: threadResponse.question,
        sqlReasoning: sqlReasoning,
        existingSQL: threadResponse.sql || '',
        threadData: threadData,
        clarificationPrompt: clarificationPrompt || '',
        projectId: project.id.toString(),
        deployId: deployId || undefined, // Pass deployId for schema access
      });

      logger.debug(
        `Created monitor generation task with ID: ${taskId.queryId}`,
      );

      // AUTO-PERSISTENCE: Create initial monitor generation record in database
      try {
        const monitorGeneration =
          await ctx.monitorService.createMonitorGeneration({
            threadResponseId: threadResponseId,
            taskId: taskId.queryId,
            status: MonitorStatus.PENDING,
            // Additional fields will be populated when results are available
          });

        // Add to background tracker for automatic persistence
        const monitorTracker = ctx.askingService.getMonitorBackgroundTracker();
        monitorTracker.addMonitorGenerationTask(
          taskId.queryId,
          threadResponseId,
          monitorGeneration.id,
        );

        logger.debug(
          `Created initial monitor generation record for task ${taskId.queryId} and added to background tracker`,
        );
      } catch (persistError: any) {
        logger.error(
          `Failed to create initial monitor generation record: ${persistError.message}`,
        );
        // Don't fail the entire operation - the task will continue without persistence
      }

      return { id: taskId.queryId };
    } catch (error: any) {
      logger.error(`Failed to generate monitor: ${error.message}`);
      throw error;
    }
  }

  public async getMonitorGenerationResult(
    _root: any,
    args: { taskId: string },
    ctx: IContext,
  ): Promise<any> {
    logger.debug(`Getting monitor generation result for task ${args.taskId}`);

    try {
      const result = await ctx.wrenAIAdaptor.getMonitorGenerationResult(
        args.taskId,
      );

      // Note: AUTO-PERSISTENCE is now handled by MonitorBackgroundTracker
      // The background tracker will automatically update the database when results are available

      return {
        status: result.status?.toUpperCase(),
        error: result.error,
        response: result.response,
        traceId: result.traceId,
      };
    } catch (error: any) {
      logger.error(`Failed to get monitor generation result: ${error.message}`);
      throw error;
    }
  }

  public async cancelMonitorGeneration(
    _root: any,
    args: { taskId: string },
    ctx: IContext,
  ): Promise<boolean> {
    logger.debug(`Cancelling monitor generation task ${args.taskId}`);

    try {
      await ctx.wrenAIAdaptor.cancelMonitorGeneration(args.taskId);
      return true;
    } catch (error: any) {
      logger.error(`Failed to cancel monitor generation: ${error.message}`);
      throw error;
    }
  }

  public async chatWithMonitor(
    _root: any,
    args: {
      data: {
        originalMessage: string;
        existingSql: string;
        chatPrompt: string;
        threadData: any;
        projectId?: string;
        queryId: string;
        monitorGenerationId?: number;
      };
    },
    ctx: IContext,
  ): Promise<{ id: string }> {
    logger.debug(
      `Starting chat with monitor refinement for query ${args.data.queryId}`,
    );

    const {
      originalMessage,
      existingSql,
      chatPrompt,
      threadData,
      projectId,
      queryId,
      monitorGenerationId,
    } = args.data;

    try {
      const project = await ctx.projectService.getCurrentProject();
      const effectiveProjectId = projectId || project.id.toString();

      // Start the chat refinement task
      const task = await ctx.askingService.chat_with_monitor({
        original_message: originalMessage,
        existing_sql: existingSql,
        chat_prompt: chatPrompt,
        thread_data: threadData,
        project_id: effectiveProjectId,
        query_id: queryId,
      });

      logger.debug(`Chat with monitor task created with id: ${task.id}`);

      // AUTO-PERSISTENCE: Create initial chat refinement record if monitorGenerationId is provided
      if (monitorGenerationId) {
        try {
          const chatRefinement =
            await ctx.monitorService.createMonitorChatRefinement({
              monitorGenerationId: monitorGenerationId,
              chatPrompt: chatPrompt,
              taskId: task.id,
              status: MonitorStatus.PENDING,
              // Additional fields will be populated when results are available
            });

          // Add to background tracker for automatic persistence
          const monitorTracker =
            ctx.askingService.getMonitorBackgroundTracker();
          monitorTracker.addChatRefinementTask(
            task.id,
            monitorGenerationId,
            chatRefinement.id,
          );

          logger.debug(
            `Created initial chat refinement record for task ${task.id} and added to background tracker`,
          );
        } catch (persistError: any) {
          logger.error(
            `Failed to create initial chat refinement record: ${persistError.message}`,
          );
          // Don't fail the entire operation - the task will continue without persistence
        }
      }

      return { id: task.id };
    } catch (error: any) {
      logger.error(`Failed to start chat with monitor: ${error.message}`);
      throw error;
    }
  }

  // Persistence Methods
  public async createMonitorGeneration(
    _root: any,
    args: { data: MonitorGenerationInput },
    ctx: IContext,
  ) {
    logger.debug(
      `Creating monitor generation for thread response ${args.data.threadResponseId}`,
    );
    return await ctx.monitorService.createMonitorGeneration(args.data);
  }

  public async updateMonitorGeneration(
    _root: any,
    args: { data: UpdateMonitorGenerationInput },
    ctx: IContext,
  ) {
    logger.debug(`Updating monitor generation ${args.data.id}`);
    return await ctx.monitorService.updateMonitorGeneration(args.data);
  }

  public async deleteMonitorGeneration(
    _root: any,
    args: { where: { id: number } },
    ctx: IContext,
  ): Promise<boolean> {
    logger.debug(`Deleting monitor generation ${args.where.id}`);
    return await ctx.monitorService.deleteMonitorGeneration(args.where.id);
  }

  public async monitorGeneration(
    _root: any,
    args: { where: { id: number } },
    ctx: IContext,
  ) {
    const result = await ctx.monitorService.getMonitorGeneration(args.where.id);
    if (!result) {
      throw new Error(`Monitor generation with id ${args.where.id} not found`);
    }
    return result;
  }

  public async monitorGenerationsByThreadResponse(
    _root: any,
    args: { where: { threadResponseId: number } },
    ctx: IContext,
  ) {
    return await ctx.monitorService.getMonitorGenerationsByThreadResponse(
      args.where.threadResponseId,
    );
  }

  public async createMonitorChatRefinement(
    _root: any,
    args: { data: MonitorChatRefinementInput },
    ctx: IContext,
  ) {
    logger.debug(
      `Creating chat refinement for monitor generation ${args.data.monitorGenerationId}`,
    );
    return await ctx.monitorService.createMonitorChatRefinement(args.data);
  }

  public async monitorChatRefinements(
    _root: any,
    args: { monitorGenerationId: number },
    ctx: IContext,
  ) {
    return await ctx.monitorService.getMonitorChatRefinements(
      args.monitorGenerationId,
    );
  }

  public async updateMonitorExpression(
    _root: any,
    args: {
      id: number;
      expression: string;
      result: boolean;
      expressionBuilderState?: any;
    },
    ctx: IContext,
  ) {
    logger.debug(`Updating monitor expression for generation ${args.id}`);
    return await ctx.monitorService.updateMonitorExpression({
      id: args.id,
      expression: args.expression,
      result: args.result,
      expressionBuilderState: args.expressionBuilderState,
    });
  }
}
