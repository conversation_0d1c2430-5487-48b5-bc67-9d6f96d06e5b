export enum MonitorStatus {
  UNDERSTANDING = 'UNDERSTANDING',
  REASONING = 'REASONING',
  GENERATING = 'GENERATING',
  VALIDATING = 'VALIDATING',
  FINISHED = 'FINISHED',
  FAILED = 'FAILED',
  STOPPED = 'STOPPED',
  PENDING = 'PENDING',
}

export interface ExpressionBuilderState {
  selectedCells?: string[];
  cellReferences?: Record<string, any>;
  lastExpression?: string;
  evaluationHistory?: Array<{
    expression: string;
    result: any;
    timestamp: Date;
  }>;
}

export interface ValidationResults {
  execution_successful: boolean;
  execution_error?: string;
  sample_result?: string;
  key_insights?: string[];
}

export interface ExpressionSuggestion {
  template: string;
  description: string;
  reasoning: string;
  fields: string[];
  conditions?: string[];
  priority?: number;
}

export interface UIState {
  isExpanded?: boolean;
  showSQL?: boolean;
  showGenerationHistory?: boolean;
}

export interface MonitorGeneration {
  id: number;
  threadResponseId: number;
  taskId?: string;
  monitorSql?: string;
  reasoningSteps?: string[];
  validationResults?: ValidationResults;
  expressionSuggestions?: ExpressionSuggestion[];
  monitorExpression?: string;
  expressionResult?: boolean;
  status: MonitorStatus;
  expressionBuilderState?: ExpressionBuilderState;
  keyInsights?: string[];
  uiState?: UIState;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface MonitorChatRefinement {
  id: number;
  monitorGenerationId: number;
  parentRefinementId?: number;
  chatPrompt: string;
  refinedSql?: string;
  reasoningSteps?: string[];
  validationResults?: ValidationResults;
  taskId?: string;
  status: MonitorStatus;
  keyInsights?: string[];
  createdAt?: Date;
  updatedAt?: Date;
}

export interface MonitorGenerationInput {
  threadResponseId: number;
  taskId?: string;
  monitorSql?: string;
  reasoningSteps?: string[];
  validationResults?: ValidationResults;
  expressionSuggestions?: ExpressionSuggestion[];
  monitorExpression?: string;
  expressionResult?: boolean;
  status?: MonitorStatus;
  expressionBuilderState?: ExpressionBuilderState;
  keyInsights?: string[];
  uiState?: UIState;
}

export interface MonitorChatRefinementInput {
  monitorGenerationId: number;
  parentRefinementId?: number;
  chatPrompt: string;
  refinedSql?: string;
  reasoningSteps?: string[];
  validationResults?: ValidationResults;
  taskId?: string;
  status?: MonitorStatus;
  keyInsights?: string[];
}

export interface UpdateMonitorGenerationInput {
  id: number;
  monitorSql?: string;
  reasoningSteps?: string[];
  validationResults?: ValidationResults;
  expressionSuggestions?: ExpressionSuggestion[];
  monitorExpression?: string;
  expressionResult?: boolean;
  status?: MonitorStatus;
  expressionBuilderState?: ExpressionBuilderState;
  keyInsights?: string[];
  uiState?: UIState;
}

export interface UpdateMonitorExpressionInput {
  id: number;
  expression: string;
  result: boolean;
  expressionBuilderState?: ExpressionBuilderState;
}
