import { <PERSON><PERSON> } from 'knex';
import {
  MonitorGenerationRepository,
  MonitorChatRefinementRepository,
  IQueryOptions,
} from '../repositories';
import {
  MonitorGeneration,
  MonitorChatRefinement,
  MonitorGenerationInput,
  MonitorChatRefinementInput,
  UpdateMonitorGenerationInput,
  UpdateMonitorExpressionInput,
  MonitorStatus,
} from '../models/monitor';
import { getLogger } from '../utils';

const logger = getLogger('MonitorService');

export interface IMonitorService {
  // Monitor Generation methods
  createMonitorGeneration(
    data: MonitorGenerationInput,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration>;

  updateMonitorGeneration(
    data: UpdateMonitorGenerationInput,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration>;

  updateMonitorExpression(
    data: UpdateMonitorExpressionInput,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration>;

  deleteMonitorGeneration(
    id: number,
    queryOptions?: IQueryOptions,
  ): Promise<boolean>;

  getMonitorGeneration(
    id: number,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration | null>;

  getMonitorGenerationsByThreadResponse(
    threadResponseId: number,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration[]>;

  getMonitorGenerationByTaskId(
    taskId: string,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration | null>;

  // Monitor Chat Refinement methods
  createMonitorChatRefinement(
    data: MonitorChatRefinementInput,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement>;

  getMonitorChatRefinements(
    monitorGenerationId: number,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement[]>;

  getChatRefinementByTaskId(
    taskId: string,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement | null>;

  // Combined operations
  getMonitorGenerationWithChatHistory(
    id: number,
    queryOptions?: IQueryOptions,
  ): Promise<
    (MonitorGeneration & { chatRefinements: MonitorChatRefinement[] }) | null
  >;

  // Task-based update methods for AI service integration
  updateMonitorGenerationByTaskId(
    taskId: string,
    data: Partial<MonitorGeneration>,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration>;

  updateChatRefinementByTaskId(
    taskId: string,
    data: Partial<MonitorChatRefinement>,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement>;
}

export class MonitorService implements IMonitorService {
  private monitorGenerationRepository: MonitorGenerationRepository;
  private monitorChatRefinementRepository: MonitorChatRefinementRepository;

  constructor(
    knex: Knex,
    monitorGenerationRepository?: MonitorGenerationRepository,
    monitorChatRefinementRepository?: MonitorChatRefinementRepository,
  ) {
    this.monitorGenerationRepository =
      monitorGenerationRepository || new MonitorGenerationRepository(knex);
    this.monitorChatRefinementRepository =
      monitorChatRefinementRepository ||
      new MonitorChatRefinementRepository(knex);
  }

  // Monitor Generation methods
  public async createMonitorGeneration(
    data: MonitorGenerationInput,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration> {
    logger.info(
      `Creating monitor generation for thread response ${data.threadResponseId}`,
    );

    // Set default status if not provided
    const monitorData = {
      ...data,
      status: data.status || MonitorStatus.PENDING,
    };

    return await this.monitorGenerationRepository.createOne(
      monitorData,
      queryOptions,
    );
  }

  public async updateMonitorGeneration(
    data: UpdateMonitorGenerationInput,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration> {
    logger.info(`Updating monitor generation ${data.id}`);

    const { id, ...updateData } = data;
    return await this.monitorGenerationRepository.updateOne(
      id,
      updateData,
      queryOptions,
    );
  }

  public async updateMonitorExpression(
    data: UpdateMonitorExpressionInput,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration> {
    logger.info(`Updating monitor expression for generation ${data.id}`);

    const { id, expression, result, expressionBuilderState } = data;
    const updateData = {
      monitorExpression: expression,
      expressionResult: result,
      expressionBuilderState,
    };

    return await this.monitorGenerationRepository.updateOne(
      id,
      updateData,
      queryOptions,
    );
  }

  public async deleteMonitorGeneration(
    id: number,
    queryOptions?: IQueryOptions,
  ): Promise<boolean> {
    logger.info(`Deleting monitor generation ${id}`);

    const deletedCount = await this.monitorGenerationRepository.deleteOne(
      id.toString(),
      queryOptions,
    );
    return deletedCount > 0;
  }

  public async getMonitorGeneration(
    id: number,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration | null> {
    return await this.monitorGenerationRepository.findOneBy(
      { id },
      queryOptions,
    );
  }

  public async getMonitorGenerationsByThreadResponse(
    threadResponseId: number,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration[]> {
    return await this.monitorGenerationRepository.findByThreadResponseId(
      threadResponseId,
      queryOptions,
    );
  }

  public async getMonitorGenerationByTaskId(
    taskId: string,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration | null> {
    return await this.monitorGenerationRepository.findByTaskId(
      taskId,
      queryOptions,
    );
  }

  // Monitor Chat Refinement methods
  public async createMonitorChatRefinement(
    data: MonitorChatRefinementInput,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement> {
    logger.info(
      `Creating chat refinement for monitor generation ${data.monitorGenerationId}`,
    );

    // Set default status if not provided
    const refinementData = {
      ...data,
      status: data.status || MonitorStatus.PENDING,
    };

    return await this.monitorChatRefinementRepository.createOne(
      refinementData,
      queryOptions,
    );
  }

  public async getMonitorChatRefinements(
    monitorGenerationId: number,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement[]> {
    return await this.monitorChatRefinementRepository.findByMonitorGenerationId(
      monitorGenerationId,
      queryOptions,
    );
  }

  public async getChatRefinementByTaskId(
    taskId: string,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement | null> {
    return await this.monitorChatRefinementRepository.findByTaskId(
      taskId,
      queryOptions,
    );
  }

  // Combined operations
  public async getMonitorGenerationWithChatHistory(
    id: number,
    queryOptions?: IQueryOptions,
  ): Promise<
    (MonitorGeneration & { chatRefinements: MonitorChatRefinement[] }) | null
  > {
    const monitorGeneration = await this.getMonitorGeneration(id, queryOptions);
    if (!monitorGeneration) {
      return null;
    }

    const chatRefinements = await this.getMonitorChatRefinements(
      id,
      queryOptions,
    );

    return {
      ...monitorGeneration,
      chatRefinements,
    };
  }

  // Task-based update methods for integration with existing AI service
  public async updateMonitorGenerationByTaskId(
    taskId: string,
    data: Partial<MonitorGeneration>,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration> {
    logger.info(`Updating monitor generation by task ID ${taskId}`);
    return await this.monitorGenerationRepository.updateByTaskId(
      taskId,
      data,
      queryOptions,
    );
  }

  public async updateChatRefinementByTaskId(
    taskId: string,
    data: Partial<MonitorChatRefinement>,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement> {
    logger.info(`Updating chat refinement by task ID ${taskId}`);
    return await this.monitorChatRefinementRepository.updateByTaskId(
      taskId,
      data,
      queryOptions,
    );
  }
}
