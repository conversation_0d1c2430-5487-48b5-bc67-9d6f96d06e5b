import { K<PERSON> } from 'knex';
import {
  BaseRepository,
  IBasicRepository,
  IQueryOptions,
} from './baseRepository';
import { camelCase, isPlainObject, mapKeys, mapValues } from 'lodash';
import { MonitorChatRefinement, MonitorStatus } from '@server/models/monitor';

export interface IMonitorChatRefinementRepository
  extends IBasicRepository<MonitorChatRefinement> {
  findByMonitorGenerationId(
    monitorGenerationId: number,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement[]>;
  findByTaskId(
    taskId: string,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement | null>;
  findByStatus(
    statuses: MonitorStatus[],
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement[]>;
  updateByTaskId(
    taskId: string,
    data: Partial<MonitorChatRefinement>,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement>;
  getChatHistory(
    monitorGenerationId: number,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement[]>;
}

export class MonitorChatRefinementRepository
  extends BaseRepository<MonitorChatRefinement>
  implements IMonitorChatRefinementRepository
{
  private readonly jsonbColumns = [
    'reasoningSteps',
    'validationResults',
    'keyInsights',
  ];

  constructor(knexPg: Knex) {
    super({ knexPg, tableName: 'monitor_chat_refinement' });
  }

  public async findByMonitorGenerationId(
    monitorGenerationId: number,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement[]> {
    const executer = queryOptions?.tx ? queryOptions.tx : this.knex;
    const query = executer(this.tableName).where({
      monitor_generation_id: monitorGenerationId,
    });

    if (queryOptions?.order) {
      query.orderBy(queryOptions.order);
    } else {
      query.orderBy('created_at', 'asc');
    }

    const result = await query;
    return result.map(this.transformFromDBData);
  }

  public async findByTaskId(
    taskId: string,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement | null> {
    const executer = queryOptions?.tx ? queryOptions.tx : this.knex;
    const result = await executer(this.tableName).where({ task_id: taskId });
    return result && result.length > 0
      ? this.transformFromDBData(result[0])
      : null;
  }

  public async findByStatus(
    statuses: MonitorStatus[],
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement[]> {
    const executer = queryOptions?.tx ? queryOptions.tx : this.knex;
    const query = executer(this.tableName).whereIn('status', statuses);

    if (queryOptions?.order) {
      query.orderBy(queryOptions.order);
    } else {
      query.orderBy('created_at', 'desc');
    }

    if (queryOptions?.limit) {
      query.limit(queryOptions.limit);
    }

    const result = await query;
    return result.map(this.transformFromDBData);
  }

  public async updateByTaskId(
    taskId: string,
    data: Partial<MonitorChatRefinement>,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement> {
    const executer = queryOptions?.tx ? queryOptions.tx : this.knex;
    const [result] = await executer(this.tableName)
      .where({ task_id: taskId })
      .update(this.transformToDBData(data))
      .returning('*');
    return this.transformFromDBData(result);
  }

  public async getChatHistory(
    monitorGenerationId: number,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorChatRefinement[]> {
    const executer = queryOptions?.tx ? queryOptions.tx : this.knex;
    const query = executer(this.tableName)
      .where({
        monitor_generation_id: monitorGenerationId,
      })
      .orderBy('created_at', 'asc');

    if (queryOptions?.limit) {
      query.limit(queryOptions.limit);
    }

    const result = await query;
    return result.map(this.transformFromDBData);
  }

  public async updateOne(
    id: string | number,
    data: Partial<{
      refinedSql: string;
      reasoningSteps: string[];
      validationResults: any;
      taskId: string;
      status: MonitorStatus;
      keyInsights: string[];
    }>,
    queryOptions?: IQueryOptions,
  ) {
    const transformedData = {
      refinedSql: data.refinedSql ? data.refinedSql : undefined,
      reasoningSteps: data.reasoningSteps
        ? JSON.stringify(data.reasoningSteps)
        : undefined,
      validationResults: data.validationResults
        ? JSON.stringify(data.validationResults)
        : undefined,
      taskId: data.taskId ? data.taskId : undefined,
      status: data.status ? data.status : undefined,
      keyInsights: data.keyInsights
        ? JSON.stringify(data.keyInsights)
        : undefined,
    };

    const executer = queryOptions?.tx ? queryOptions.tx : this.knex;
    const [result] = await executer(this.tableName)
      .where({ id })
      .update(this.transformToDBData(transformedData as any))
      .returning('*');
    return this.transformFromDBData(result);
  }

  protected override transformFromDBData = (
    data: any,
  ): MonitorChatRefinement => {
    if (!isPlainObject(data)) {
      throw new Error('Unexpected dbdata');
    }
    const camelCaseData = mapKeys(data, (_value, key) => camelCase(key));
    const formattedData = mapValues(camelCaseData, (value, key) => {
      if (this.jsonbColumns.includes(key)) {
        // The value from SQLite will be string type, while the value from PG is JSON object
        if (typeof value === 'string') {
          return value ? JSON.parse(value) : value;
        } else {
          return value;
        }
      }
      return value;
    }) as MonitorChatRefinement;
    return formattedData;
  };
}
