import { K<PERSON> } from 'knex';
import {
  BaseRepository,
  IBasicRepository,
  IQueryOptions,
} from './baseRepository';
import { camelCase, isPlainObject, mapKeys, mapValues } from 'lodash';
import { MonitorGeneration, MonitorStatus } from '@server/models/monitor';

export interface IMonitorGenerationRepository
  extends IBasicRepository<MonitorGeneration> {
  findByThreadResponseId(
    threadResponseId: number,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration[]>;
  findByTaskId(
    taskId: string,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration | null>;
  findByStatus(
    statuses: MonitorStatus[],
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration[]>;
  updateByTaskId(
    taskId: string,
    data: Partial<MonitorGeneration>,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration>;
}

export class MonitorGenerationRepository
  extends BaseRepository<MonitorGeneration>
  implements IMonitorGenerationRepository
{
  private readonly jsonbColumns = [
    'reasoningSteps',
    'validationResults',
    'expressionSuggestions',
    'expressionBuilderState',
    'keyInsights',
    'uiState',
  ];

  constructor(knexPg: Knex) {
    super({ knexPg, tableName: 'monitor_generation' });
  }

  public async findByThreadResponseId(
    threadResponseId: number,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration[]> {
    const executer = queryOptions?.tx ? queryOptions.tx : this.knex;
    const query = executer(this.tableName).where({
      thread_response_id: threadResponseId,
    });

    if (queryOptions?.order) {
      query.orderBy(queryOptions.order);
    } else {
      query.orderBy('created_at', 'desc');
    }

    const result = await query;
    return result.map(this.transformFromDBData);
  }

  public async findByTaskId(
    taskId: string,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration | null> {
    const executer = queryOptions?.tx ? queryOptions.tx : this.knex;
    const result = await executer(this.tableName).where({ task_id: taskId });
    return result && result.length > 0
      ? this.transformFromDBData(result[0])
      : null;
  }

  public async findByStatus(
    statuses: MonitorStatus[],
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration[]> {
    const executer = queryOptions?.tx ? queryOptions.tx : this.knex;
    const query = executer(this.tableName).whereIn('status', statuses);

    if (queryOptions?.order) {
      query.orderBy(queryOptions.order);
    } else {
      query.orderBy('created_at', 'desc');
    }

    if (queryOptions?.limit) {
      query.limit(queryOptions.limit);
    }

    const result = await query;
    return result.map(this.transformFromDBData);
  }

  public async updateByTaskId(
    taskId: string,
    data: Partial<MonitorGeneration>,
    queryOptions?: IQueryOptions,
  ): Promise<MonitorGeneration> {
    const executer = queryOptions?.tx ? queryOptions.tx : this.knex;
    const [result] = await executer(this.tableName)
      .where({ task_id: taskId })
      .update(this.transformToDBData(data))
      .returning('*');
    return this.transformFromDBData(result);
  }

  public async updateOne(
    id: string | number,
    data: Partial<{
      taskId: string;
      monitorSql: string;
      reasoningSteps: string[];
      validationResults: any;
      expressionSuggestions: any;
      monitorExpression: string;
      expressionResult: boolean;
      status: MonitorStatus;
      expressionBuilderState: any;
      keyInsights: string[];
      uiState: any;
    }>,
    queryOptions?: IQueryOptions,
  ) {
    const transformedData = {
      taskId: data.taskId ? data.taskId : undefined,
      monitorSql: data.monitorSql ? data.monitorSql : undefined,
      reasoningSteps: data.reasoningSteps
        ? JSON.stringify(data.reasoningSteps)
        : undefined,
      validationResults: data.validationResults
        ? JSON.stringify(data.validationResults)
        : undefined,
      expressionSuggestions: data.expressionSuggestions
        ? JSON.stringify(data.expressionSuggestions)
        : undefined,
      monitorExpression: data.monitorExpression
        ? data.monitorExpression
        : undefined,
      expressionResult:
        data.expressionResult !== undefined ? data.expressionResult : undefined,
      status: data.status ? data.status : undefined,
      expressionBuilderState: data.expressionBuilderState
        ? JSON.stringify(data.expressionBuilderState)
        : undefined,
      keyInsights: data.keyInsights
        ? JSON.stringify(data.keyInsights)
        : undefined,
      uiState: data.uiState ? JSON.stringify(data.uiState) : undefined,
    };

    const executer = queryOptions?.tx ? queryOptions.tx : this.knex;
    const [result] = await executer(this.tableName)
      .where({ id })
      .update(this.transformToDBData(transformedData as any))
      .returning('*');
    return this.transformFromDBData(result);
  }

  protected override transformFromDBData = (data: any): MonitorGeneration => {
    if (!isPlainObject(data)) {
      throw new Error('Unexpected dbdata');
    }
    const camelCaseData = mapKeys(data, (_value, key) => camelCase(key));
    const formattedData = mapValues(camelCaseData, (value, key) => {
      if (this.jsonbColumns.includes(key)) {
        // The value from SQLite will be string type, while the value from PG is JSON object
        if (typeof value === 'string') {
          return value ? JSON.parse(value) : value;
        } else {
          return value;
        }
      }
      return value;
    }) as MonitorGeneration;
    return formattedData;
  };
}
