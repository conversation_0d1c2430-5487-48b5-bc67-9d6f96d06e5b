import * as Types from './__types__';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GenerateMonitorMutationVariables = Types.Exact<{
  data: Types.GenerateMonitorInput;
}>;


export type GenerateMonitorMutation = { __typename?: 'Mutation', generateMonitor: { __typename?: 'Task', id: string } };

export type GetMonitorGenerationResultQueryVariables = Types.Exact<{
  taskId: Types.Scalars['String'];
}>;


export type GetMonitorGenerationResultQuery = { __typename?: 'Query', getMonitorGenerationResult: { __typename?: 'MonitorGenerationTask', status: Types.MonitorGenerationStatus, traceId?: string | null, error?: { __typename?: 'Error', code?: string | null, message?: string | null } | null, response?: { __typename?: 'MonitorGenerationResult', monitor_sql?: string | null, reasoning_steps?: Array<string> | null, template_suggestions?: Array<{ __typename?: 'TemplateSuggestion', template: string, description: string, reasoning: string, fields: Array<string>, conditions?: Array<string> | null, priority?: number | null }> | null, validation_results?: { __typename?: 'ValidationResult', execution_successful?: boolean | null, execution_error?: string | null, sample_result?: string | null, key_insights?: Array<string> | null } | null } | null } };

export type CancelMonitorGenerationMutationVariables = Types.Exact<{
  taskId: Types.Scalars['String'];
}>;


export type CancelMonitorGenerationMutation = { __typename?: 'Mutation', cancelMonitorGeneration: boolean };

export type ChatWithMonitorMutationVariables = Types.Exact<{
  data: Types.ChatWithMonitorInput;
}>;


export type ChatWithMonitorMutation = { __typename?: 'Mutation', chatWithMonitor: { __typename?: 'Task', id: string } };


export const GenerateMonitorDocument = gql`
    mutation GenerateMonitor($data: GenerateMonitorInput!) {
  generateMonitor(data: $data) {
    id
  }
}
    `;
export type GenerateMonitorMutationFn = Apollo.MutationFunction<GenerateMonitorMutation, GenerateMonitorMutationVariables>;

/**
 * __useGenerateMonitorMutation__
 *
 * To run a mutation, you first call `useGenerateMonitorMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useGenerateMonitorMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [generateMonitorMutation, { data, loading, error }] = useGenerateMonitorMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useGenerateMonitorMutation(baseOptions?: Apollo.MutationHookOptions<GenerateMonitorMutation, GenerateMonitorMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<GenerateMonitorMutation, GenerateMonitorMutationVariables>(GenerateMonitorDocument, options);
      }
export type GenerateMonitorMutationHookResult = ReturnType<typeof useGenerateMonitorMutation>;
export type GenerateMonitorMutationResult = Apollo.MutationResult<GenerateMonitorMutation>;
export type GenerateMonitorMutationOptions = Apollo.BaseMutationOptions<GenerateMonitorMutation, GenerateMonitorMutationVariables>;
export const GetMonitorGenerationResultDocument = gql`
    query GetMonitorGenerationResult($taskId: String!) {
  getMonitorGenerationResult(taskId: $taskId) {
    status
    error {
      code
      message
    }
    response {
      monitor_sql
      reasoning_steps
      template_suggestions {
        template
        description
        reasoning
        fields
        conditions
        priority
      }
      validation_results {
        execution_successful
        execution_error
        sample_result
        key_insights
      }
    }
    traceId
  }
}
    `;

/**
 * __useGetMonitorGenerationResultQuery__
 *
 * To run a query within a React component, call `useGetMonitorGenerationResultQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMonitorGenerationResultQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMonitorGenerationResultQuery({
 *   variables: {
 *      taskId: // value for 'taskId'
 *   },
 * });
 */
export function useGetMonitorGenerationResultQuery(baseOptions: Apollo.QueryHookOptions<GetMonitorGenerationResultQuery, GetMonitorGenerationResultQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetMonitorGenerationResultQuery, GetMonitorGenerationResultQueryVariables>(GetMonitorGenerationResultDocument, options);
      }
export function useGetMonitorGenerationResultLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetMonitorGenerationResultQuery, GetMonitorGenerationResultQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetMonitorGenerationResultQuery, GetMonitorGenerationResultQueryVariables>(GetMonitorGenerationResultDocument, options);
        }
export type GetMonitorGenerationResultQueryHookResult = ReturnType<typeof useGetMonitorGenerationResultQuery>;
export type GetMonitorGenerationResultLazyQueryHookResult = ReturnType<typeof useGetMonitorGenerationResultLazyQuery>;
export type GetMonitorGenerationResultQueryResult = Apollo.QueryResult<GetMonitorGenerationResultQuery, GetMonitorGenerationResultQueryVariables>;
export const CancelMonitorGenerationDocument = gql`
    mutation CancelMonitorGeneration($taskId: String!) {
  cancelMonitorGeneration(taskId: $taskId)
}
    `;
export type CancelMonitorGenerationMutationFn = Apollo.MutationFunction<CancelMonitorGenerationMutation, CancelMonitorGenerationMutationVariables>;

/**
 * __useCancelMonitorGenerationMutation__
 *
 * To run a mutation, you first call `useCancelMonitorGenerationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCancelMonitorGenerationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [cancelMonitorGenerationMutation, { data, loading, error }] = useCancelMonitorGenerationMutation({
 *   variables: {
 *      taskId: // value for 'taskId'
 *   },
 * });
 */
export function useCancelMonitorGenerationMutation(baseOptions?: Apollo.MutationHookOptions<CancelMonitorGenerationMutation, CancelMonitorGenerationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CancelMonitorGenerationMutation, CancelMonitorGenerationMutationVariables>(CancelMonitorGenerationDocument, options);
      }
export type CancelMonitorGenerationMutationHookResult = ReturnType<typeof useCancelMonitorGenerationMutation>;
export type CancelMonitorGenerationMutationResult = Apollo.MutationResult<CancelMonitorGenerationMutation>;
export type CancelMonitorGenerationMutationOptions = Apollo.BaseMutationOptions<CancelMonitorGenerationMutation, CancelMonitorGenerationMutationVariables>;
export const ChatWithMonitorDocument = gql`
    mutation ChatWithMonitor($data: ChatWithMonitorInput!) {
  chatWithMonitor(data: $data) {
    id
  }
}
    `;
export type ChatWithMonitorMutationFn = Apollo.MutationFunction<ChatWithMonitorMutation, ChatWithMonitorMutationVariables>;

/**
 * __useChatWithMonitorMutation__
 *
 * To run a mutation, you first call `useChatWithMonitorMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useChatWithMonitorMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [chatWithMonitorMutation, { data, loading, error }] = useChatWithMonitorMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useChatWithMonitorMutation(baseOptions?: Apollo.MutationHookOptions<ChatWithMonitorMutation, ChatWithMonitorMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ChatWithMonitorMutation, ChatWithMonitorMutationVariables>(ChatWithMonitorDocument, options);
      }
export type ChatWithMonitorMutationHookResult = ReturnType<typeof useChatWithMonitorMutation>;
export type ChatWithMonitorMutationResult = Apollo.MutationResult<ChatWithMonitorMutation>;
export type ChatWithMonitorMutationOptions = Apollo.BaseMutationOptions<ChatWithMonitorMutation, ChatWithMonitorMutationVariables>;