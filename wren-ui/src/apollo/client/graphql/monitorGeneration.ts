import { gql } from '@apollo/client';

export const GENERATE_MONITOR = gql`
  mutation GenerateMonitor($data: GenerateMonitorInput!) {
    generateMonitor(data: $data) {
      id
    }
  }
`;

export const GET_MONITOR_GENERATION_RESULT = gql`
  query GetMonitorGenerationResult($taskId: String!) {
    getMonitorGenerationResult(taskId: $taskId) {
      status
      error {
        code
        shortMessage
        message
        stacktrace
      }
      response {
        monitor_sql
        reasoning_steps
        template_suggestions {
          template
          description
          reasoning
          fields
          conditions
          priority
        }
        validation_results {
          execution_successful
          execution_error
          sample_result
          key_insights
        }
      }
      traceId
    }
  }
`;

export const CANCEL_MONITOR_GENERATION = gql`
  mutation CancelMonitorGeneration($taskId: String!) {
    cancelMonitorGeneration(taskId: $taskId)
  }
`;
