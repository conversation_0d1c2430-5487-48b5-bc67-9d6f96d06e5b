import { gql } from '@apollo/client';

export const CREATE_MONITOR_GENERATION = gql`
  mutation CreateMonitorGeneration($data: CreateMonitorGenerationInput!) {
    createMonitorGeneration(data: $data) {
      id
      threadResponseId
      taskId
      monitorSql
      reasoningSteps
      validationResults {
        execution_successful
        execution_error
        sample_result
        key_insights
      }
      expressionSuggestions {
        template
        description
        reasoning
        fields
        conditions
        priority
      }
      monitorExpression
      expressionResult
      status
      expressionBuilderState {
        selectedCells
        cellReferences
        lastExpression
        evaluationHistory
      }
      keyInsights
      uiState {
        isExpanded
        showSQL
        showGenerationHistory
      }
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_MONITOR_GENERATION = gql`
  mutation UpdateMonitorGeneration($data: UpdateMonitorGenerationInput!) {
    updateMonitorGeneration(data: $data) {
      id
      threadResponseId
      taskId
      monitorSql
      reasoningSteps
      validationResults {
        execution_successful
        execution_error
        sample_result
        key_insights
      }
      expressionSuggestions {
        template
        description
        reasoning
        fields
        conditions
        priority
      }
      monitorExpression
      expressionResult
      status
      expressionBuilderState {
        selectedCells
        cellReferences
        lastExpression
        evaluationHistory
      }
      keyInsights
      uiState {
        isExpanded
        showSQL
        showGenerationHistory
      }
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_MONITOR_EXPRESSION = gql`
  mutation UpdateMonitorExpression(
    $id: Int!
    $expression: String!
    $result: Boolean!
    $expressionBuilderState: JSON
  ) {
    updateMonitorExpression(
      id: $id
      expression: $expression
      result: $result
      expressionBuilderState: $expressionBuilderState
    ) {
      id
      monitorExpression
      expressionResult
      expressionBuilderState {
        selectedCells
        cellReferences
        lastExpression
        evaluationHistory
      }
      updatedAt
    }
  }
`;

export const DELETE_MONITOR_GENERATION = gql`
  mutation DeleteMonitorGeneration($where: MonitorWhereInput!) {
    deleteMonitorGeneration(where: $where)
  }
`;

export const MONITOR_GENERATIONS_BY_THREAD_RESPONSE = gql`
  query MonitorGenerationsByThreadResponse($where: ThreadResponseWhereInput!) {
    monitorGenerationsByThreadResponse(where: $where) {
      id
      threadResponseId
      taskId
      monitorSql
      reasoningSteps
      validationResults {
        execution_successful
        execution_error
        sample_result
        key_insights
      }
      expressionSuggestions {
        template
        description
        reasoning
        fields
        conditions
        priority
      }
      monitorExpression
      expressionResult
      status
      expressionBuilderState {
        selectedCells
        cellReferences
        lastExpression
        evaluationHistory
      }
      keyInsights
      uiState {
        isExpanded
        showSQL
        showGenerationHistory
      }
      createdAt
      updatedAt
      chatRefinements {
        id
        monitorGenerationId
        parentRefinementId
        chatPrompt
        refinedSql
        reasoningSteps
        validationResults {
          execution_successful
          execution_error
          sample_result
          key_insights
        }
        taskId
        status
        keyInsights
        createdAt
        updatedAt
      }
    }
  }
`;

export const MONITOR_GENERATION = gql`
  query MonitorGeneration($id: Int!) {
    monitorGeneration(id: $id) {
      id
      threadResponseId
      taskId
      monitorSql
      reasoningSteps
      validationResults {
        execution_successful
        execution_error
        sample_result
        key_insights
      }
      expressionSuggestions {
        template
        description
        reasoning
        fields
        conditions
        priority
      }
      monitorExpression
      expressionResult
      status
      expressionBuilderState {
        selectedCells
        cellReferences
        lastExpression
        evaluationHistory
      }
      keyInsights
      uiState {
        isExpanded
        showSQL
        showGenerationHistory
      }
      createdAt
      updatedAt
      chatRefinements {
        id
        monitorGenerationId
        parentRefinementId
        chatPrompt
        refinedSql
        reasoningSteps
        validationResults {
          execution_successful
          execution_error
          sample_result
          key_insights
        }
        taskId
        status
        keyInsights
        createdAt
        updatedAt
      }
    }
  }
`;

export const CREATE_MONITOR_CHAT_REFINEMENT = gql`
  mutation CreateMonitorChatRefinement(
    $data: CreateMonitorChatRefinementInput!
  ) {
    createMonitorChatRefinement(data: $data) {
      id
      monitorGenerationId
      parentRefinementId
      chatPrompt
      refinedSql
      reasoningSteps
      validationResults {
        execution_successful
        execution_error
        sample_result
        key_insights
      }
      taskId
      status
      keyInsights
      createdAt
      updatedAt
    }
  }
`;
