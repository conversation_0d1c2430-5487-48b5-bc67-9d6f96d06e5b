import * as Types from './__types__';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type CreateMonitorGenerationMutationVariables = Types.Exact<{
  data: Types.CreateMonitorGenerationInput;
}>;


export type CreateMonitorGenerationMutation = { __typename?: 'Mutation', createMonitorGeneration: { __typename?: 'MonitorGeneration', id: number, threadResponseId: number, taskId?: string | null, monitorSql?: string | null, reasoningSteps?: Array<string> | null, monitorExpression?: string | null, expressionResult?: boolean | null, status: Types.MonitorGenerationStatus, keyInsights?: Array<string> | null, createdAt: string, updatedAt: string, validationResults?: { __typename?: 'ValidationResult', execution_successful?: boolean | null, execution_error?: string | null, sample_result?: string | null, key_insights?: Array<string> | null } | null, expressionSuggestions?: Array<{ __typename?: 'TemplateSuggestion', template: string, description: string, reasoning: string, fields: Array<string>, conditions?: Array<string> | null, priority?: number | null }> | null, expressionBuilderState?: { __typename?: 'ExpressionBuilderState', selectedCells?: Array<string> | null, cellReferences?: any | null, lastExpression?: string | null, evaluationHistory?: any | null } | null, uiState?: { __typename?: 'UIState', isExpanded?: boolean | null, showSQL?: boolean | null, showGenerationHistory?: boolean | null } | null } };

export type UpdateMonitorGenerationMutationVariables = Types.Exact<{
  data: Types.UpdateMonitorGenerationInput;
}>;


export type UpdateMonitorGenerationMutation = { __typename?: 'Mutation', updateMonitorGeneration: { __typename?: 'MonitorGeneration', id: number, threadResponseId: number, taskId?: string | null, monitorSql?: string | null, reasoningSteps?: Array<string> | null, monitorExpression?: string | null, expressionResult?: boolean | null, status: Types.MonitorGenerationStatus, keyInsights?: Array<string> | null, createdAt: string, updatedAt: string, validationResults?: { __typename?: 'ValidationResult', execution_successful?: boolean | null, execution_error?: string | null, sample_result?: string | null, key_insights?: Array<string> | null } | null, expressionSuggestions?: Array<{ __typename?: 'TemplateSuggestion', template: string, description: string, reasoning: string, fields: Array<string>, conditions?: Array<string> | null, priority?: number | null }> | null, expressionBuilderState?: { __typename?: 'ExpressionBuilderState', selectedCells?: Array<string> | null, cellReferences?: any | null, lastExpression?: string | null, evaluationHistory?: any | null } | null, uiState?: { __typename?: 'UIState', isExpanded?: boolean | null, showSQL?: boolean | null, showGenerationHistory?: boolean | null } | null } };

export type UpdateMonitorExpressionMutationVariables = Types.Exact<{
  id: Types.Scalars['Int'];
  expression: Types.Scalars['String'];
  result: Types.Scalars['Boolean'];
  expressionBuilderState?: Types.InputMaybe<Types.Scalars['JSON']>;
}>;


export type UpdateMonitorExpressionMutation = { __typename?: 'Mutation', updateMonitorExpression: { __typename?: 'MonitorGeneration', id: number, monitorExpression?: string | null, expressionResult?: boolean | null, updatedAt: string, expressionBuilderState?: { __typename?: 'ExpressionBuilderState', selectedCells?: Array<string> | null, cellReferences?: any | null, lastExpression?: string | null, evaluationHistory?: any | null } | null } };

export type DeleteMonitorGenerationMutationVariables = Types.Exact<{
  where: Types.MonitorWhereInput;
}>;


export type DeleteMonitorGenerationMutation = { __typename?: 'Mutation', deleteMonitorGeneration: boolean };

export type MonitorGenerationsByThreadResponseQueryVariables = Types.Exact<{
  where: Types.ThreadResponseWhereInput;
}>;


export type MonitorGenerationsByThreadResponseQuery = { __typename?: 'Query', monitorGenerationsByThreadResponse: Array<{ __typename?: 'MonitorGeneration', id: number, threadResponseId: number, taskId?: string | null, monitorSql?: string | null, reasoningSteps?: Array<string> | null, monitorExpression?: string | null, expressionResult?: boolean | null, status: Types.MonitorGenerationStatus, keyInsights?: Array<string> | null, createdAt: string, updatedAt: string, validationResults?: { __typename?: 'ValidationResult', execution_successful?: boolean | null, execution_error?: string | null, sample_result?: string | null, key_insights?: Array<string> | null } | null, expressionSuggestions?: Array<{ __typename?: 'TemplateSuggestion', template: string, description: string, reasoning: string, fields: Array<string>, conditions?: Array<string> | null, priority?: number | null }> | null, expressionBuilderState?: { __typename?: 'ExpressionBuilderState', selectedCells?: Array<string> | null, cellReferences?: any | null, lastExpression?: string | null, evaluationHistory?: any | null } | null, uiState?: { __typename?: 'UIState', isExpanded?: boolean | null, showSQL?: boolean | null, showGenerationHistory?: boolean | null } | null, chatRefinements?: Array<{ __typename?: 'MonitorChatRefinement', id: number, monitorGenerationId: number, parentRefinementId?: number | null, chatPrompt: string, refinedSql?: string | null, reasoningSteps?: Array<string> | null, taskId?: string | null, status: Types.MonitorGenerationStatus, keyInsights?: Array<string> | null, createdAt: string, updatedAt: string, validationResults?: { __typename?: 'ValidationResult', execution_successful?: boolean | null, execution_error?: string | null, sample_result?: string | null, key_insights?: Array<string> | null } | null }> | null }> };

export type MonitorGenerationQueryVariables = Types.Exact<{
  id: Types.Scalars['Int'];
}>;


export type MonitorGenerationQuery = { __typename?: 'Query', monitorGeneration: { __typename?: 'MonitorGeneration', id: number, threadResponseId: number, taskId?: string | null, monitorSql?: string | null, reasoningSteps?: Array<string> | null, monitorExpression?: string | null, expressionResult?: boolean | null, status: Types.MonitorGenerationStatus, keyInsights?: Array<string> | null, createdAt: string, updatedAt: string, validationResults?: { __typename?: 'ValidationResult', execution_successful?: boolean | null, execution_error?: string | null, sample_result?: string | null, key_insights?: Array<string> | null } | null, expressionSuggestions?: Array<{ __typename?: 'TemplateSuggestion', template: string, description: string, reasoning: string, fields: Array<string>, conditions?: Array<string> | null, priority?: number | null }> | null, expressionBuilderState?: { __typename?: 'ExpressionBuilderState', selectedCells?: Array<string> | null, cellReferences?: any | null, lastExpression?: string | null, evaluationHistory?: any | null } | null, uiState?: { __typename?: 'UIState', isExpanded?: boolean | null, showSQL?: boolean | null, showGenerationHistory?: boolean | null } | null, chatRefinements?: Array<{ __typename?: 'MonitorChatRefinement', id: number, monitorGenerationId: number, parentRefinementId?: number | null, chatPrompt: string, refinedSql?: string | null, reasoningSteps?: Array<string> | null, taskId?: string | null, status: Types.MonitorGenerationStatus, keyInsights?: Array<string> | null, createdAt: string, updatedAt: string, validationResults?: { __typename?: 'ValidationResult', execution_successful?: boolean | null, execution_error?: string | null, sample_result?: string | null, key_insights?: Array<string> | null } | null }> | null } };

export type CreateMonitorChatRefinementMutationVariables = Types.Exact<{
  data: Types.CreateMonitorChatRefinementInput;
}>;


export type CreateMonitorChatRefinementMutation = { __typename?: 'Mutation', createMonitorChatRefinement: { __typename?: 'MonitorChatRefinement', id: number, monitorGenerationId: number, parentRefinementId?: number | null, chatPrompt: string, refinedSql?: string | null, reasoningSteps?: Array<string> | null, taskId?: string | null, status: Types.MonitorGenerationStatus, keyInsights?: Array<string> | null, createdAt: string, updatedAt: string, validationResults?: { __typename?: 'ValidationResult', execution_successful?: boolean | null, execution_error?: string | null, sample_result?: string | null, key_insights?: Array<string> | null } | null } };


export const CreateMonitorGenerationDocument = gql`
    mutation CreateMonitorGeneration($data: CreateMonitorGenerationInput!) {
  createMonitorGeneration(data: $data) {
    id
    threadResponseId
    taskId
    monitorSql
    reasoningSteps
    validationResults {
      execution_successful
      execution_error
      sample_result
      key_insights
    }
    expressionSuggestions {
      template
      description
      reasoning
      fields
      conditions
      priority
    }
    monitorExpression
    expressionResult
    status
    expressionBuilderState {
      selectedCells
      cellReferences
      lastExpression
      evaluationHistory
    }
    keyInsights
    uiState {
      isExpanded
      showSQL
      showGenerationHistory
    }
    createdAt
    updatedAt
  }
}
    `;
export type CreateMonitorGenerationMutationFn = Apollo.MutationFunction<CreateMonitorGenerationMutation, CreateMonitorGenerationMutationVariables>;

/**
 * __useCreateMonitorGenerationMutation__
 *
 * To run a mutation, you first call `useCreateMonitorGenerationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateMonitorGenerationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createMonitorGenerationMutation, { data, loading, error }] = useCreateMonitorGenerationMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useCreateMonitorGenerationMutation(baseOptions?: Apollo.MutationHookOptions<CreateMonitorGenerationMutation, CreateMonitorGenerationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreateMonitorGenerationMutation, CreateMonitorGenerationMutationVariables>(CreateMonitorGenerationDocument, options);
      }
export type CreateMonitorGenerationMutationHookResult = ReturnType<typeof useCreateMonitorGenerationMutation>;
export type CreateMonitorGenerationMutationResult = Apollo.MutationResult<CreateMonitorGenerationMutation>;
export type CreateMonitorGenerationMutationOptions = Apollo.BaseMutationOptions<CreateMonitorGenerationMutation, CreateMonitorGenerationMutationVariables>;
export const UpdateMonitorGenerationDocument = gql`
    mutation UpdateMonitorGeneration($data: UpdateMonitorGenerationInput!) {
  updateMonitorGeneration(data: $data) {
    id
    threadResponseId
    taskId
    monitorSql
    reasoningSteps
    validationResults {
      execution_successful
      execution_error
      sample_result
      key_insights
    }
    expressionSuggestions {
      template
      description
      reasoning
      fields
      conditions
      priority
    }
    monitorExpression
    expressionResult
    status
    expressionBuilderState {
      selectedCells
      cellReferences
      lastExpression
      evaluationHistory
    }
    keyInsights
    uiState {
      isExpanded
      showSQL
      showGenerationHistory
    }
    createdAt
    updatedAt
  }
}
    `;
export type UpdateMonitorGenerationMutationFn = Apollo.MutationFunction<UpdateMonitorGenerationMutation, UpdateMonitorGenerationMutationVariables>;

/**
 * __useUpdateMonitorGenerationMutation__
 *
 * To run a mutation, you first call `useUpdateMonitorGenerationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateMonitorGenerationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateMonitorGenerationMutation, { data, loading, error }] = useUpdateMonitorGenerationMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useUpdateMonitorGenerationMutation(baseOptions?: Apollo.MutationHookOptions<UpdateMonitorGenerationMutation, UpdateMonitorGenerationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UpdateMonitorGenerationMutation, UpdateMonitorGenerationMutationVariables>(UpdateMonitorGenerationDocument, options);
      }
export type UpdateMonitorGenerationMutationHookResult = ReturnType<typeof useUpdateMonitorGenerationMutation>;
export type UpdateMonitorGenerationMutationResult = Apollo.MutationResult<UpdateMonitorGenerationMutation>;
export type UpdateMonitorGenerationMutationOptions = Apollo.BaseMutationOptions<UpdateMonitorGenerationMutation, UpdateMonitorGenerationMutationVariables>;
export const UpdateMonitorExpressionDocument = gql`
    mutation UpdateMonitorExpression($id: Int!, $expression: String!, $result: Boolean!, $expressionBuilderState: JSON) {
  updateMonitorExpression(
    id: $id
    expression: $expression
    result: $result
    expressionBuilderState: $expressionBuilderState
  ) {
    id
    monitorExpression
    expressionResult
    expressionBuilderState {
      selectedCells
      cellReferences
      lastExpression
      evaluationHistory
    }
    updatedAt
  }
}
    `;
export type UpdateMonitorExpressionMutationFn = Apollo.MutationFunction<UpdateMonitorExpressionMutation, UpdateMonitorExpressionMutationVariables>;

/**
 * __useUpdateMonitorExpressionMutation__
 *
 * To run a mutation, you first call `useUpdateMonitorExpressionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateMonitorExpressionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateMonitorExpressionMutation, { data, loading, error }] = useUpdateMonitorExpressionMutation({
 *   variables: {
 *      id: // value for 'id'
 *      expression: // value for 'expression'
 *      result: // value for 'result'
 *      expressionBuilderState: // value for 'expressionBuilderState'
 *   },
 * });
 */
export function useUpdateMonitorExpressionMutation(baseOptions?: Apollo.MutationHookOptions<UpdateMonitorExpressionMutation, UpdateMonitorExpressionMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UpdateMonitorExpressionMutation, UpdateMonitorExpressionMutationVariables>(UpdateMonitorExpressionDocument, options);
      }
export type UpdateMonitorExpressionMutationHookResult = ReturnType<typeof useUpdateMonitorExpressionMutation>;
export type UpdateMonitorExpressionMutationResult = Apollo.MutationResult<UpdateMonitorExpressionMutation>;
export type UpdateMonitorExpressionMutationOptions = Apollo.BaseMutationOptions<UpdateMonitorExpressionMutation, UpdateMonitorExpressionMutationVariables>;
export const DeleteMonitorGenerationDocument = gql`
    mutation DeleteMonitorGeneration($where: MonitorWhereInput!) {
  deleteMonitorGeneration(where: $where)
}
    `;
export type DeleteMonitorGenerationMutationFn = Apollo.MutationFunction<DeleteMonitorGenerationMutation, DeleteMonitorGenerationMutationVariables>;

/**
 * __useDeleteMonitorGenerationMutation__
 *
 * To run a mutation, you first call `useDeleteMonitorGenerationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteMonitorGenerationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteMonitorGenerationMutation, { data, loading, error }] = useDeleteMonitorGenerationMutation({
 *   variables: {
 *      where: // value for 'where'
 *   },
 * });
 */
export function useDeleteMonitorGenerationMutation(baseOptions?: Apollo.MutationHookOptions<DeleteMonitorGenerationMutation, DeleteMonitorGenerationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteMonitorGenerationMutation, DeleteMonitorGenerationMutationVariables>(DeleteMonitorGenerationDocument, options);
      }
export type DeleteMonitorGenerationMutationHookResult = ReturnType<typeof useDeleteMonitorGenerationMutation>;
export type DeleteMonitorGenerationMutationResult = Apollo.MutationResult<DeleteMonitorGenerationMutation>;
export type DeleteMonitorGenerationMutationOptions = Apollo.BaseMutationOptions<DeleteMonitorGenerationMutation, DeleteMonitorGenerationMutationVariables>;
export const MonitorGenerationsByThreadResponseDocument = gql`
    query MonitorGenerationsByThreadResponse($where: ThreadResponseWhereInput!) {
  monitorGenerationsByThreadResponse(where: $where) {
    id
    threadResponseId
    taskId
    monitorSql
    reasoningSteps
    validationResults {
      execution_successful
      execution_error
      sample_result
      key_insights
    }
    expressionSuggestions {
      template
      description
      reasoning
      fields
      conditions
      priority
    }
    monitorExpression
    expressionResult
    status
    expressionBuilderState {
      selectedCells
      cellReferences
      lastExpression
      evaluationHistory
    }
    keyInsights
    uiState {
      isExpanded
      showSQL
      showGenerationHistory
    }
    createdAt
    updatedAt
    chatRefinements {
      id
      monitorGenerationId
      parentRefinementId
      chatPrompt
      refinedSql
      reasoningSteps
      validationResults {
        execution_successful
        execution_error
        sample_result
        key_insights
      }
      taskId
      status
      keyInsights
      createdAt
      updatedAt
    }
  }
}
    `;

/**
 * __useMonitorGenerationsByThreadResponseQuery__
 *
 * To run a query within a React component, call `useMonitorGenerationsByThreadResponseQuery` and pass it any options that fit your needs.
 * When your component renders, `useMonitorGenerationsByThreadResponseQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMonitorGenerationsByThreadResponseQuery({
 *   variables: {
 *      where: // value for 'where'
 *   },
 * });
 */
export function useMonitorGenerationsByThreadResponseQuery(baseOptions: Apollo.QueryHookOptions<MonitorGenerationsByThreadResponseQuery, MonitorGenerationsByThreadResponseQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<MonitorGenerationsByThreadResponseQuery, MonitorGenerationsByThreadResponseQueryVariables>(MonitorGenerationsByThreadResponseDocument, options);
      }
export function useMonitorGenerationsByThreadResponseLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<MonitorGenerationsByThreadResponseQuery, MonitorGenerationsByThreadResponseQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<MonitorGenerationsByThreadResponseQuery, MonitorGenerationsByThreadResponseQueryVariables>(MonitorGenerationsByThreadResponseDocument, options);
        }
export type MonitorGenerationsByThreadResponseQueryHookResult = ReturnType<typeof useMonitorGenerationsByThreadResponseQuery>;
export type MonitorGenerationsByThreadResponseLazyQueryHookResult = ReturnType<typeof useMonitorGenerationsByThreadResponseLazyQuery>;
export type MonitorGenerationsByThreadResponseQueryResult = Apollo.QueryResult<MonitorGenerationsByThreadResponseQuery, MonitorGenerationsByThreadResponseQueryVariables>;
export const MonitorGenerationDocument = gql`
    query MonitorGeneration($id: Int!) {
  monitorGeneration(id: $id) {
    id
    threadResponseId
    taskId
    monitorSql
    reasoningSteps
    validationResults {
      execution_successful
      execution_error
      sample_result
      key_insights
    }
    expressionSuggestions {
      template
      description
      reasoning
      fields
      conditions
      priority
    }
    monitorExpression
    expressionResult
    status
    expressionBuilderState {
      selectedCells
      cellReferences
      lastExpression
      evaluationHistory
    }
    keyInsights
    uiState {
      isExpanded
      showSQL
      showGenerationHistory
    }
    createdAt
    updatedAt
    chatRefinements {
      id
      monitorGenerationId
      parentRefinementId
      chatPrompt
      refinedSql
      reasoningSteps
      validationResults {
        execution_successful
        execution_error
        sample_result
        key_insights
      }
      taskId
      status
      keyInsights
      createdAt
      updatedAt
    }
  }
}
    `;

/**
 * __useMonitorGenerationQuery__
 *
 * To run a query within a React component, call `useMonitorGenerationQuery` and pass it any options that fit your needs.
 * When your component renders, `useMonitorGenerationQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMonitorGenerationQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useMonitorGenerationQuery(baseOptions: Apollo.QueryHookOptions<MonitorGenerationQuery, MonitorGenerationQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<MonitorGenerationQuery, MonitorGenerationQueryVariables>(MonitorGenerationDocument, options);
      }
export function useMonitorGenerationLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<MonitorGenerationQuery, MonitorGenerationQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<MonitorGenerationQuery, MonitorGenerationQueryVariables>(MonitorGenerationDocument, options);
        }
export type MonitorGenerationQueryHookResult = ReturnType<typeof useMonitorGenerationQuery>;
export type MonitorGenerationLazyQueryHookResult = ReturnType<typeof useMonitorGenerationLazyQuery>;
export type MonitorGenerationQueryResult = Apollo.QueryResult<MonitorGenerationQuery, MonitorGenerationQueryVariables>;
export const CreateMonitorChatRefinementDocument = gql`
    mutation CreateMonitorChatRefinement($data: CreateMonitorChatRefinementInput!) {
  createMonitorChatRefinement(data: $data) {
    id
    monitorGenerationId
    parentRefinementId
    chatPrompt
    refinedSql
    reasoningSteps
    validationResults {
      execution_successful
      execution_error
      sample_result
      key_insights
    }
    taskId
    status
    keyInsights
    createdAt
    updatedAt
  }
}
    `;
export type CreateMonitorChatRefinementMutationFn = Apollo.MutationFunction<CreateMonitorChatRefinementMutation, CreateMonitorChatRefinementMutationVariables>;

/**
 * __useCreateMonitorChatRefinementMutation__
 *
 * To run a mutation, you first call `useCreateMonitorChatRefinementMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateMonitorChatRefinementMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createMonitorChatRefinementMutation, { data, loading, error }] = useCreateMonitorChatRefinementMutation({
 *   variables: {
 *      data: // value for 'data'
 *   },
 * });
 */
export function useCreateMonitorChatRefinementMutation(baseOptions?: Apollo.MutationHookOptions<CreateMonitorChatRefinementMutation, CreateMonitorChatRefinementMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreateMonitorChatRefinementMutation, CreateMonitorChatRefinementMutationVariables>(CreateMonitorChatRefinementDocument, options);
      }
export type CreateMonitorChatRefinementMutationHookResult = ReturnType<typeof useCreateMonitorChatRefinementMutation>;
export type CreateMonitorChatRefinementMutationResult = Apollo.MutationResult<CreateMonitorChatRefinementMutation>;
export type CreateMonitorChatRefinementMutationOptions = Apollo.BaseMutationOptions<CreateMonitorChatRefinementMutation, CreateMonitorChatRefinementMutationVariables>;