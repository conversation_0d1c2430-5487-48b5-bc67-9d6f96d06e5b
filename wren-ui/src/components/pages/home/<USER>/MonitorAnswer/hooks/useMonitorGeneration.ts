import { useEffect } from 'react';
import { message } from '@/import/antd';
import { useGetMonitorGenerationResultLazyQuery } from '@/apollo/client/graphql/monitors.generated';
import { usePreviewSqlMutation } from '@/apollo/client/graphql/home.generated';
import usePromptThreadStore from '../../store';
import {
  UseMonitorGenerationReturn,
  MonitorGenerationResult,
  MonitorStatus,
} from '../types';

export const useMonitorGeneration = (
  threadResponseId: number,
): UseMonitorGenerationReturn => {
  const promptThreadStore = usePromptThreadStore();
  const monitorTaskId =
    promptThreadStore.monitorTaskIds?.[threadResponseId] || null;

  const [previewSql, previewSqlResult] = usePreviewSqlMutation({
    onError: (error) => console.error(error),
  });

  // Monitor generation hooks
  const [
    getMonitorResult,
    { data: monitorResultData, error: monitorResultError, stopPolling },
  ] = useGetMonitorGenerationResultLazyQuery({
    pollInterval: 2000,
    notifyOnNetworkStatusChange: true,
  });

  // Extract results
  const monitorStatus = monitorResultData?.getMonitorGenerationResult
    ?.status as MonitorStatus | undefined;
  const monitorFinished = monitorStatus === 'FINISHED';
  const monitorFailed = monitorStatus === 'FAILED';
  const monitorInProgress = Boolean(
    monitorTaskId && !monitorFinished && !monitorFailed,
  );
  const monitorResult = monitorResultData?.getMonitorGenerationResult
    ?.response as MonitorGenerationResult;

  // Start polling when we have a task ID
  useEffect(() => {
    if (monitorTaskId && !monitorFinished && !monitorFailed) {
      getMonitorResult({ variables: { taskId: monitorTaskId } });
    }
  }, [monitorTaskId, getMonitorResult, monitorFinished, monitorFailed]);

  // Stop polling when complete
  useEffect(() => {
    if (
      (monitorFinished || monitorFailed || monitorResultError) &&
      stopPolling
    ) {
      stopPolling();
    }
  }, [monitorFinished, monitorFailed, monitorResultError, stopPolling]);

  // Get current generation stage
  const getCurrentStage = (): number => {
    if (!monitorInProgress) return -1;

    console.log('Monitor status:', monitorStatus);

    switch (monitorStatus) {
      case 'UNDERSTANDING':
        return 0;
      case 'REASONING':
        return 1;
      case 'GENERATING':
        return 2;
      case 'VALIDATING':
        return 3;
      default:
        console.log('Unknown monitor status:', monitorStatus);
        return 0;
    }
  };

  const handleTestSQL = async (): Promise<void> => {
    if (!monitorResult?.monitor_sql) {
      message.warning('No SQL to test');
      return;
    }

    await previewSql({
      variables: {
        data: {
          sql: monitorResult.monitor_sql,
          limit: 500,
        },
      },
    });
  };

  return {
    monitorTaskId,
    monitorStatus,
    monitorFinished,
    monitorFailed,
    monitorInProgress,
    monitorResult,
    monitorResultError,
    getCurrentStage,
    handleTestSQL,
    previewSqlResult,
  };
};
