import { useState } from 'react';
import { message } from '@/import/antd';
import { usePreviewSqlMutation } from '@/apollo/client/graphql/home.generated';
import { UseSQLPreviewReturn, MonitorGenerationResult } from '../types';

interface UseSQLPreviewProps {
  monitorResult?: MonitorGenerationResult;
}

export const useSQLPreview = ({
  monitorResult,
}: UseSQLPreviewProps): UseSQLPreviewReturn => {
  const [showSQL, setShowSQL] = useState(false);

  const [previewSql, previewSqlResult] = usePreviewSqlMutation({
    onError: (error) => console.error(error),
  });

  const handleTestSQL = async (): Promise<void> => {
    if (!monitorResult?.monitor_sql) {
      message.warning('No SQL to test');
      return;
    }

    await previewSql({
      variables: {
        data: {
          sql: monitorResult.monitor_sql,
          limit: 500,
        },
      },
    });
  };

  return {
    showSQL,
    setShowSQL,
    previewSqlResult,
    handleTestSQL,
  };
};
