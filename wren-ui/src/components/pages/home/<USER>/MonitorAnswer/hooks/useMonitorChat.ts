import { useState, useEffect } from 'react';
import { message } from '@/import/antd';
import {
  useGetMonitorGenerationResultLazyQuery,
  useChatWithMonitorMutation,
} from '@/apollo/client/graphql/monitors.generated';
import {
  UseMonitorChatReturn,
  MonitorGeneration,
  MonitorGenerationResult,
  MonitorStatus,
} from '../types';

interface UseMonitorChatProps {
  threadResponse: {
    id: number;
    question?: string;
    answerDetail?: any;
    chartDetail?: any;
    breakdownDetail?: any;
  };
  latestMonitorResult?: MonitorGenerationResult;
  monitorFinished: boolean;
}

export const useMonitorChat = ({
  threadResponse,
  latestMonitorResult,
  monitorFinished,
}: UseMonitorChatProps): UseMonitorChatReturn => {
  const [chatInput, setChatInput] = useState('');
  const [isChatEnabled, setIsChatEnabled] = useState(false);
  const [isChatSubmitting, setIsChatSubmitting] = useState(false);
  const [generations, setGenerations] = useState<MonitorGeneration[]>([]);
  const [showGenerationHistory, setShowGenerationHistory] = useState(false);
  const [activeTaskIds, setActiveTaskIds] = useState<Set<string>>(new Set());
  const [currentPollingTaskId, setCurrentPollingTaskId] = useState<
    string | null
  >(null);
  const [monitorStatuses, setMonitorStatuses] = useState<
    Record<string, MonitorStatus>
  >({});

  const [
    getMonitorResult,
    { data: chatMonitorResultData, stopPolling: stopChatPolling },
  ] = useGetMonitorGenerationResultLazyQuery({
    pollInterval: 2000,
    notifyOnNetworkStatusChange: true,
  });

  const [chatWithMonitor] = useChatWithMonitorMutation();

  // Start polling for chat tasks when they are created
  useEffect(() => {
    const activeChatTasks = generations.filter(
      (gen) => !gen.result?.monitor_sql && activeTaskIds.has(gen.id),
    );

    if (activeChatTasks.length > 0) {
      // Poll the most recent active chat task
      const latestChatTask = activeChatTasks[activeChatTasks.length - 1];
      if (latestChatTask.id !== currentPollingTaskId) {
        console.log(
          'Starting Apollo polling for chat task:',
          latestChatTask.id,
        );
        setCurrentPollingTaskId(latestChatTask.id);
        getMonitorResult({ variables: { taskId: latestChatTask.id } });
      }
    }
  }, [generations, activeTaskIds, currentPollingTaskId, getMonitorResult]);

  // Handle chat task completion
  useEffect(() => {
    if (
      chatMonitorResultData?.getMonitorGenerationResult &&
      currentPollingTaskId
    ) {
      const chatResult = chatMonitorResultData.getMonitorGenerationResult;
      const chatStatus = chatResult.status as MonitorStatus;
      const chatResponse = chatResult.response as MonitorGenerationResult;

      // Update the monitor status for the current task
      setMonitorStatuses((prev) => ({
        ...prev,
        [currentPollingTaskId]: chatStatus,
      }));

      // Check if this is a completed chat task
      if (chatStatus === 'FINISHED' && chatResponse?.monitor_sql) {
        // Find the specific generation that matches the current polling task ID
        const targetGeneration = generations.find(
          (gen) => gen.id === currentPollingTaskId,
        );

        if (targetGeneration && activeTaskIds.has(currentPollingTaskId)) {
          // Update the generation with the completed result
          setGenerations((prev) => {
            const existsIndex = prev.findIndex(
              (g) => g.id === currentPollingTaskId,
            );
            if (existsIndex >= 0) {
              const updated = [...prev];
              updated[existsIndex] = {
                ...updated[existsIndex],
                result: chatResponse,
              };
              return updated;
            }
            return prev;
          });

          // Remove from active tasks and stop chat submission loading
          setActiveTaskIds((prev) => {
            if (!prev.has(currentPollingTaskId)) return prev;
            const newSet = new Set(prev);
            newSet.delete(currentPollingTaskId);
            return newSet;
          });

          // Clear the current polling task ID and stop Apollo polling
          setCurrentPollingTaskId(null);
          setIsChatSubmitting(false);

          if (stopChatPolling) {
            stopChatPolling();
          }

          console.log(
            'Chat task completed, stopping Apollo polling for task:',
            currentPollingTaskId,
          );
        }
      }
    }
  }, [chatMonitorResultData, currentPollingTaskId, generations, activeTaskIds]);

  // Stop polling when no more active chat tasks
  useEffect(() => {
    const activeChatTasks = generations.filter(
      (gen) => !gen.result?.monitor_sql && activeTaskIds.has(gen.id),
    );

    if (
      activeChatTasks.length === 0 &&
      currentPollingTaskId &&
      stopChatPolling
    ) {
      console.log('No more active chat tasks, stopping Apollo polling');
      stopChatPolling();
      setCurrentPollingTaskId(null);
    }
  }, [generations, activeTaskIds, currentPollingTaskId, stopChatPolling]);

  // Enable chat after first successful generation
  useEffect(() => {
    if (monitorFinished && latestMonitorResult) {
      setIsChatEnabled(true);
    }
  }, [monitorFinished, latestMonitorResult]);

  // Helper function to format timestamp
  const formatTimestamp = (timestamp: Date): string => {
    return timestamp.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const handleChatSubmit = async (): Promise<void> => {
    if (!chatInput.trim() || !latestMonitorResult || isChatSubmitting) {
      return;
    }

    setIsChatSubmitting(true);

    try {
      // Get the latest generation's SQL (from the most recent refinement)
      const currentSql = latestMonitorResult.monitor_sql || '';

      // Start a new chat refinement task
      const chatResult = await chatWithMonitor({
        variables: {
          data: {
            originalMessage: threadResponse?.question || '',
            existingSql: currentSql,
            chatPrompt: chatInput,
            threadData: {
              answerDetail: threadResponse?.answerDetail || {},
              chartDetail: threadResponse?.chartDetail || {},
              breakdownDetail: threadResponse?.breakdownDetail || {},
            },
            projectId: null, // Add project ID if available
            queryId: `${threadResponse.id}_chat_${Date.now()}`,
          },
        },
      });

      if (chatResult.data?.chatWithMonitor?.id) {
        const chatTaskId = chatResult.data.chatWithMonitor.id;

        // Add the chat generation to the generations array with pending status
        setGenerations((prev) => [
          ...prev,
          {
            id: chatTaskId,
            result: {} as MonitorGenerationResult, // Will be populated when complete
            timestamp: new Date(),
            chatPrompt: chatInput,
          },
        ]);

        // Add to active tasks for polling
        setActiveTaskIds((prev) => {
          if (prev.has(chatTaskId)) return prev;
          const newSet = new Set(prev);
          newSet.add(chatTaskId);
          return newSet;
        });

        // Clear the chat input
        setChatInput('');

        message.success('Chat refinement started');
      }
    } catch (error: any) {
      setIsChatSubmitting(false);
      message.error(`Chat refinement failed: ${error.message}`);
    }
  };

  return {
    chatInput,
    setChatInput,
    isChatEnabled,
    isChatSubmitting,
    generations,
    showGenerationHistory,
    setShowGenerationHistory,
    activeTaskIds,
    handleChatSubmit,
    formatTimestamp,
    setGenerations,
    setActiveTaskIds,
    setIsChatSubmitting,
    monitorStatuses,
  };
};
