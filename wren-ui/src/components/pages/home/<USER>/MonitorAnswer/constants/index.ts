import { StageDescription } from '../types';

export const GENERATION_STAGES = [
  'Understanding',
  'Reasoning',
  'Generating',
  'Validating',
];

export const STAGE_DESCRIPTIONS: Record<string, StageDescription> = {
  UNDERSTANDING: {
    title: 'Understanding Query',
    subtitle: 'Analyzing your question and data patterns...',
    details: 'Examining table schemas and business context.',
  },
  REASONING: {
    title: 'Planning Monitor',
    subtitle: 'Determining the best monitoring approach...',
    details: 'Analyzing metrics and detection thresholds.',
  },
  GENERATING: {
    title: 'Creating Monitor',
    subtitle: 'Writing monitoring query with comparison logic...',
    details: 'Building comparative queries and change detection.',
  },
  VALIDATING: {
    title: 'Validating Monitor',
    subtitle: 'Testing the monitor query...',
    details: 'Running validation and checking for issues.',
  },
};
