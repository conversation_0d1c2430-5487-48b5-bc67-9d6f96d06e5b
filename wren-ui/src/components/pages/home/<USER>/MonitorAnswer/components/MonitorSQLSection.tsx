import { Button, Typography, Empty, message } from '@/import/antd';
import EyeOutlined from '@ant-design/icons/EyeOutlined';
import Bar<PERSON>hartOutlined from '@ant-design/icons/BarChartOutlined';
import dynamic from 'next/dynamic';
import { format } from 'sql-formatter';
import SelectablePreviewData from '@/components/dataPreview/SelectablePreviewData';
import { BinocularsIcon } from '@/utils/icons';
import { SQLSection } from '../styles';
import { MonitorGenerationResult } from '../types';

const SQLCodeBlock = dynamic(() => import('@/components/code/SQLCodeBlock'), {
  ssr: false,
});

const { Title } = Typography;

interface MonitorSQLSectionProps {
  monitorResult?: MonitorGenerationResult;
  showSQL: boolean;
  setShowSQL: (show: boolean) => void;
  previewSqlResult: any;
  onTestSQL: () => void;
  onExpressionChange?: (expression: string, result: boolean | null) => void;
}

export const MonitorSQLSection: React.FC<MonitorSQLSectionProps> = ({
  monitorResult,
  showSQL,
  setShowSQL,
  previewSqlResult,
  onTestSQL,
  onExpressionChange,
}) => {
  return (
    <>
      {/* SQL Section */}
      <SQLSection>
        <div className="sql-header">
          <Title className="sql-title" level={5}>
            <BarChartOutlined
              style={{ marginRight: 8, color: 'var(--geekblue-6)' }}
            />
            Generated Monitor SQL
          </Title>
          <Button
            className="view-toggle"
            type="link"
            icon={<EyeOutlined />}
            onClick={() => setShowSQL(!showSQL)}
          >
            {showSQL ? 'Hide SQL' : 'View SQL'}
          </Button>
        </div>

        {showSQL && monitorResult?.monitor_sql && (
          <SQLCodeBlock
            code={format(monitorResult.monitor_sql)}
            showLineNumbers
            maxHeight="400"
            copyable
            onCopy={() => message.success('SQL copied to clipboard')}
          />
        )}
      </SQLSection>

      {/* Query Results Section */}
      <div style={{ marginTop: 20 }}>
        <Button
          size="small"
          icon={
            <BinocularsIcon
              style={{
                paddingBottom: 2,
                marginRight: 8,
              }}
            />
          }
          loading={previewSqlResult.loading}
          onClick={onTestSQL}
          disabled={!monitorResult?.monitor_sql}
        >
          View results
        </Button>
        {(previewSqlResult?.data?.previewSql ||
          previewSqlResult.loading ||
          previewSqlResult.error) && (
          <div style={{ marginTop: 16, marginBottom: 24 }}>
            <SelectablePreviewData
              error={previewSqlResult.error}
              loading={previewSqlResult.loading}
              previewData={previewSqlResult?.data?.previewSql}
              onExpressionChange={onExpressionChange}
              locale={{
                emptyText: (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="Sorry, we couldn't find any records that match your search criteria."
                  />
                ),
              }}
            />
            <div style={{ textAlign: 'right', marginTop: 8 }}>
              <Typography.Text style={{ fontSize: 14, color: 'var(--gray-6)' }}>
                Showing up to 500 rows
              </Typography.Text>
            </div>
          </div>
        )}
      </div>
    </>
  );
};
