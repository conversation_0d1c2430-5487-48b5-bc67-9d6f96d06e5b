import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Spin } from '@/import/antd';
import { MonitorGenerationTrigger } from './MonitorGenerationTrigger';
import { MonitorGenerationProgress } from './MonitorGenerationProgress';
import { MonitorGenerationError } from './MonitorGenerationError';
import { MonitorAnswerInstance } from './MonitorAnswerInstance';
import { MonitorChatInterface } from './MonitorChatInterface';
import { useMonitorGeneration } from '../hooks/useMonitorGeneration';
import { useMonitorChat } from '../hooks/useMonitorChat';
import useMonitorGenerationPersistence from '@/hooks/useMonitorGenerationPersistence';
import { MonitorContainer } from '../styles';
import { MonitorAnswerProps, MonitorGeneration } from '../types';
import usePromptThreadStore from '../../store';

export const MonitorAnswerContainer: React.FC<MonitorAnswerProps> = ({
  threadResponse,
}) => {
  const [expandedInstances, setExpandedInstances] = useState<Set<string>>(
    new Set(),
  );

  const promptThreadStore = usePromptThreadStore();
  // const onGenerateMonitorAnswer = promptThreadStore.onGenerateMonitorAnswer;

  // Enhanced persistence hooks
  const persistence = useMonitorGenerationPersistence();

  // Fallback to original hooks for compatibility during transition
  const monitorGeneration = useMonitorGeneration(threadResponse.id);
  const monitorChat = useMonitorChat({
    threadResponse,
    latestMonitorResult: monitorGeneration.monitorResult,
    monitorFinished: monitorGeneration.monitorFinished,
  });

  // Load persisted generations when component mounts or threadResponse changes
  useEffect(() => {
    if (threadResponse?.id) {
      persistence.loadPersistedGenerations(threadResponse.id);
    }
  }, [threadResponse?.id, persistence.loadPersistedGenerations]);

  useEffect(() => {
    return () => {
      persistence.resetGeneration();
    };
  }, [threadResponse?.id, persistence.resetGeneration]);

  // Enhanced expression change handler with persistence
  const handleExpressionChange = async (
    expression: string,
    result: boolean | null,
    monitorGenerationId?: number,
  ) => {
    if (monitorGenerationId && result !== null) {
      try {
        await persistence.updateExpression(
          monitorGenerationId,
          expression,
          result,
        );
      } catch (error) {
        console.error('Failed to persist expression change:', error);
      }
    }
  };

  // Enhanced monitor generation with persistence
  const handleGenerateMonitor = async () => {
    if (!threadResponse?.id) return;

    // Use the enhanced persistence-aware generation
    await persistence.generateMonitor({
      threadResponseId: threadResponse.id,
    });
  };

  const sql = threadResponse?.sql || '';

  // Create instances array: combining persisted and in-progress generations
  const instances: MonitorGeneration[] = [];

  // Add persisted generations (convert from GraphQL format to component format)
  const persistedInstances = persistence.persistedGenerations.map((gen) => ({
    id: gen.id.toString(),
    result: {
      monitor_sql: gen.monitorSql,
      reasoning_steps: gen.reasoningSteps,
      template_suggestions: gen.expressionSuggestions,
      validation_results: gen.validationResults
        ? {
            execution_successful:
              gen.validationResults.execution_successful || false,
            execution_error: gen.validationResults.execution_error || undefined,
            sample_result: gen.validationResults.sample_result || undefined,
          }
        : undefined,
    },
    timestamp: new Date(gen.createdAt),
    chatPrompt: undefined, // Initial generations have no chat prompt
    persistenceId: gen.id, // Keep track of database ID
    status: gen.status,
    expression: gen.monitorExpression,
    expressionResult: gen.expressionResult,
  }));

  instances.push(...persistedInstances);

  // Add in-progress generation if it exists and isn't already persisted
  if (monitorGeneration.monitorFinished && monitorGeneration.monitorResult) {
    const inProgressExists = instances.some(
      (inst) => inst.id === monitorGeneration.monitorTaskId,
    );

    if (!inProgressExists) {
      instances.push({
        id: monitorGeneration.monitorTaskId || 'initial',
        result: monitorGeneration.monitorResult,
        timestamp: new Date(),
        chatPrompt: undefined,
      });
    }
  }

  // Add chat refinements (both persisted and in-progress)
  const chatInstances = monitorChat.generations
    .filter((gen) => gen.result?.monitor_sql)
    .map((gen) => ({
      ...gen,
      persistenceId: undefined, // Chat refinements might not be persisted yet
    }));

  instances.push(...chatInstances);

  // Auto-expand the most recent instance
  const latestInstanceId = instances[instances.length - 1]?.id;
  if (latestInstanceId && !expandedInstances.has(latestInstanceId)) {
    setExpandedInstances((prev) => {
      const newSet = new Set(prev);
      // Collapse all others and expand the latest
      newSet.clear();
      newSet.add(latestInstanceId);
      return newSet;
    });
  }

  const toggleExpanded = (instanceId: string) => {
    setExpandedInstances((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(instanceId)) {
        newSet.delete(instanceId);
      } else {
        newSet.add(instanceId);
      }
      return newSet;
    });
  };

  // Show loading state while loading persisted data
  if (persistence.loadingPersisted) {
    return (
      <MonitorContainer>
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          <Spin size="large" />
          <div style={{ marginTop: '1rem' }}>Loading saved monitors...</div>
        </div>
      </MonitorContainer>
    );
  }

  // Render generation trigger if no generations exist
  if (
    !monitorGeneration.monitorTaskId &&
    instances.length === 0 &&
    !persistence.isGenerating
  ) {
    return (
      <MonitorGenerationTrigger onGenerateMonitor={handleGenerateMonitor} />
    );
  }

  // Render generation in progress (for both initial and persistence-aware generation)
  // Show progress regardless of existing instances
  if (monitorGeneration.monitorInProgress || persistence.isGenerating) {
    const currentStage = monitorGeneration.getCurrentStage();

    // If there are no instances, show progress as the main content
    if (instances.length === 0) {
      return (
        <MonitorGenerationProgress
          currentStage={currentStage}
          monitorStatus={
            persistence.generationStatus || monitorGeneration.monitorStatus
          }
        />
      );
    }

    // If there are existing instances, we'll show progress at the top of the instances list
    // This will be handled in the main render section below
  }

  // Render error state (check both legacy and persistence errors)
  const hasError =
    monitorGeneration.monitorFailed ||
    persistence.generationError ||
    persistence.persistenceError;
  if (hasError && instances.length === 0) {
    const error =
      persistence.generationError ||
      persistence.persistenceError ||
      monitorGeneration.monitorResultError;
    return (
      <MonitorGenerationError error={error} onRetry={handleGenerateMonitor} />
    );
  }

  // Render no SQL available
  if (!sql) {
    return (
      <MonitorContainer>
        <Alert
          message="No SQL query available"
          description="Please generate a SQL query first to create a monitor."
          type="info"
        />
      </MonitorContainer>
    );
  }

  // Render instances with chat interface
  return (
    <MonitorContainer>
      {/* Show progress at the top when generation is in progress and there are existing instances */}
      {(monitorGeneration.monitorInProgress || persistence.isGenerating) &&
        instances.length > 0 && (
          <div style={{ marginBottom: '1rem' }}>
            <MonitorGenerationProgress
              currentStage={monitorGeneration.getCurrentStage()}
              monitorStatus={
                persistence.generationStatus || monitorGeneration.monitorStatus
              }
            />
          </div>
        )}

      {instances.map((instance, index) => (
        <MonitorAnswerInstance
          key={instance.id}
          instance={instance}
          isExpanded={expandedInstances.has(instance.id)}
          isLatest={index === instances.length - 1}
          instanceNumber={index + 1}
          onToggleExpanded={() => toggleExpanded(instance.id)}
          threadResponse={threadResponse}
          onExpressionChange={(expression, result) =>
            handleExpressionChange(
              expression,
              result,
              (instance as any).persistenceId,
            )
          }
        />
      ))}

      {/* Show persistence errors if any */}
      {persistence.persistenceError && (
        <Alert
          message="Database Error"
          description={`Failed to sync with database: ${persistence.persistenceError.message}`}
          type="warning"
          showIcon
          style={{ marginBottom: '1rem' }}
        />
      )}

      {/* Chat Interface - only show if we have at least one completed instance */}
      {instances.length > 0 && (
        <MonitorChatInterface
          chatHook={monitorChat}
          latestResult={instances[instances.length - 1]?.result}
          threadResponse={threadResponse}
          monitorStatuses={monitorChat.monitorStatuses}
        />
      )}
    </MonitorContainer>
  );
};
