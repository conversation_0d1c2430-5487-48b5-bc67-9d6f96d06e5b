import { Button, Typography, Tag } from '@/import/antd';
import HistoryOutlined from '@ant-design/icons/HistoryOutlined';
import ClockCircleOutlined from '@ant-design/icons/ClockCircleOutlined';
import { GenerationHistorySection } from '../styles';
import { MonitorGeneration } from '../types';

const { Title, Text } = Typography;

interface MonitorGenerationHistoryProps {
  generations: MonitorGeneration[];
  showGenerationHistory: boolean;
  setShowGenerationHistory: (show: boolean) => void;
  formatTimestamp: (timestamp: Date) => string;
}

export const MonitorGenerationHistory: React.FC<
  MonitorGenerationHistoryProps
> = ({
  generations,
  showGenerationHistory,
  setShowGenerationHistory,
  formatTimestamp,
}) => {
  if (generations.length <= 1) {
    return null;
  }

  return (
    <GenerationHistorySection>
      <div className="history-header">
        <HistoryOutlined style={{ color: 'var(--geekblue-6)' }} />
        <Title className="history-title" level={5}>
          Generation History
        </Title>
        <Button
          type="link"
          size="small"
          onClick={() => setShowGenerationHistory(!showGenerationHistory)}
        >
          {showGenerationHistory ? 'Hide' : 'Show'} ({generations.length}{' '}
          generations)
        </Button>
      </div>

      {showGenerationHistory && (
        <div>
          {generations.map((generation, index) => {
            const isLatest = index === generations.length - 1;
            const isChatGeneration = Boolean(generation.chatPrompt);

            return (
              <div
                key={generation.id}
                className={`generation-item ${isLatest ? 'current' : ''}`}
              >
                <div className="generation-header">
                  <div className="generation-info">
                    <span className="generation-number">#{index + 1}</span>
                    <span className="generation-type">
                      {isChatGeneration
                        ? 'Chat Refinement'
                        : 'Initial Generation'}
                    </span>
                    {isLatest && <Tag color="blue">Current</Tag>}
                  </div>
                  <div className="generation-time">
                    <ClockCircleOutlined style={{ marginRight: 4 }} />
                    {formatTimestamp(generation.timestamp)}
                  </div>
                </div>

                <div className="generation-content">
                  {isChatGeneration && generation.chatPrompt && (
                    <div className="chat-prompt">
                      <div className="prompt-label">Chat Input:</div>
                      {generation.chatPrompt}
                    </div>
                  )}

                  {generation.result?.monitor_sql && (
                    <div>
                      <Text
                        strong
                        style={{
                          fontSize: '14px',
                          marginBottom: '8px',
                          display: 'block',
                        }}
                      >
                        Generated SQL:
                      </Text>
                      <div className="sql-preview">
                        {generation.result.monitor_sql}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </GenerationHistorySection>
  );
};
