import { <PERSON><PERSON>, Button } from '@/import/antd';
import { MonitorContainer } from '../styles';

interface MonitorGenerationErrorProps {
  error?: any;
  onRetry: () => void;
}

export const MonitorGenerationError: React.FC<MonitorGenerationErrorProps> = ({
  error,
  onRetry,
}) => {
  return (
    <MonitorContainer>
      <Alert
        message="Monitor generation failed"
        description={error?.message || 'An error occurred during generation.'}
        type="error"
        showIcon
        action={
          <Button type="primary" onClick={onRetry}>
            Retry
          </Button>
        }
      />
    </MonitorContainer>
  );
};
