import { Spin, Typography } from '@/import/antd';
import { MonitorContainer, GenerationCard } from '../styles';
import { GENERATION_STAGES, STAGE_DESCRIPTIONS } from '../constants';
import { MonitorStatus } from '../types';

const { Text } = Typography;

interface MonitorGenerationProgressProps {
  currentStage: number;
  monitorStatus?: MonitorStatus;
}

export const MonitorGenerationProgress: React.FC<
  MonitorGenerationProgressProps
> = ({ currentStage, monitorStatus }) => {
  const stageDescription =
    monitorStatus && STAGE_DESCRIPTIONS[monitorStatus]
      ? STAGE_DESCRIPTIONS[monitorStatus]
      : {
          title: 'Generating Monitor',
          subtitle:
            'AI is analyzing your data and creating an intelligent monitoring system...',
          details: 'Processing your request...',
        };

  return (
    <MonitorContainer>
      <GenerationCard>
        <div className="generation-content">
          <div className="generation-title">{stageDescription.title}</div>
          <div className="generation-subtitle">{stageDescription.subtitle}</div>

          <div className="loading-content">
            <Spin size="large" />
            <Text
              type="secondary"
              style={{ fontSize: '14px', marginTop: '8px' }}
            >
              {GENERATION_STAGES[currentStage] || 'Processing'}...
            </Text>

            <Text
              type="secondary"
              style={{
                fontSize: '12px',
                marginTop: '4px',
                textAlign: 'center',
                maxWidth: '400px',
                color: 'var(--gray-6)',
              }}
            >
              {stageDescription.details}
            </Text>

            <div className="loading-stages">
              {GENERATION_STAGES.map((stage, index) => (
                <div key={stage} className="stage-indicator">
                  <div
                    className={`stage-dot ${
                      index < currentStage
                        ? 'completed'
                        : index === currentStage
                          ? 'active'
                          : ''
                    }`}
                  />
                  <span
                    className={`stage-label ${
                      index < currentStage
                        ? 'completed'
                        : index === currentStage
                          ? 'active'
                          : ''
                    }`}
                  >
                    {stage}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </GenerationCard>
    </MonitorContainer>
  );
};
