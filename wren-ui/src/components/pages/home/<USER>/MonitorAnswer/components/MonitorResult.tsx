import { Button, Typography, Tag, Alert, message } from '@/import/antd';
import CheckCircleOutlined from '@ant-design/icons/CheckCircleOutlined';
import ExclamationCircleOutlined from '@ant-design/icons/ExclamationCircleOutlined';
import PlayCircleOutlined from '@ant-design/icons/PlayCircleOutlined';
import { MonitorContainer, ResultCard } from '../styles';
import { MonitorSQLSection } from './MonitorSQLSection';
import { MonitorInsights } from './MonitorInsights';
import { MonitorChat } from './MonitorChat';
import { MonitorGenerationHistory } from './MonitorGenerationHistory';
import {
  MonitorGenerationResult,
  UseMonitorChatReturn,
  UseSQLPreviewReturn,
} from '../types';

const { Text } = Typography;

interface MonitorResultProps {
  monitorResult?: MonitorGenerationResult;
  chatHook: UseMonitorChatReturn;
  sqlPreviewHook: UseSQLPreviewReturn;
}

export const MonitorResult: React.FC<MonitorResultProps> = ({
  monitorResult,
  chatHook,
  sqlPreviewHook,
}) => {
  return (
    <MonitorContainer>
      <ResultCard>
        <div className="result-header">
          <div className="status-badge">
            <CheckCircleOutlined style={{ color: 'var(--green-6)' }} />
            <Text strong>Monitor Generated Successfully</Text>
          </div>
          <div className="actions">
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={() => message.info('Deploy functionality coming soon')}
            >
              Configure & Deploy Monitor
            </Button>
          </div>
        </div>

        {/* Validation Status */}
        {monitorResult?.validation_results && (
          <div style={{ marginBottom: 16 }}>
            {monitorResult.validation_results.execution_successful ? (
              <Tag icon={<CheckCircleOutlined />} color="success">
                SQL Validated Successfully
              </Tag>
            ) : (
              <Tag icon={<ExclamationCircleOutlined />} color="error">
                SQL Validation Failed
              </Tag>
            )}
          </div>
        )}

        {/* SQL Section */}
        <MonitorSQLSection
          monitorResult={monitorResult}
          showSQL={sqlPreviewHook.showSQL}
          setShowSQL={sqlPreviewHook.setShowSQL}
          previewSqlResult={sqlPreviewHook.previewSqlResult}
          onTestSQL={sqlPreviewHook.handleTestSQL}
        />

        {/* Validation Error Details */}
        {monitorResult?.validation_results?.execution_error && (
          <Alert
            type="error"
            message="SQL Validation Error"
            description={monitorResult.validation_results.execution_error}
            showIcon
          />
        )}

        {/* AI Insights */}
        <MonitorInsights monitorResult={monitorResult} />

        {/* Generation History */}
        <MonitorGenerationHistory
          generations={chatHook.generations}
          showGenerationHistory={chatHook.showGenerationHistory}
          setShowGenerationHistory={chatHook.setShowGenerationHistory}
          formatTimestamp={chatHook.formatTimestamp}
        />

        {/* Chat with Monitor Feature */}
        <MonitorChat
          chatInput={chatHook.chatInput}
          setChatInput={chatHook.setChatInput}
          isChatEnabled={chatHook.isChatEnabled}
          isChatSubmitting={chatHook.isChatSubmitting}
          generations={chatHook.generations}
          activeTaskIds={chatHook.activeTaskIds}
          onChatSubmit={chatHook.handleChatSubmit}
        />
      </ResultCard>
    </MonitorContainer>
  );
};
