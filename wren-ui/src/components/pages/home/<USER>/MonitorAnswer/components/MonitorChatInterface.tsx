import { Button, Input, Typography, Card, message } from '@/import/antd';
import MessageOutlined from '@ant-design/icons/MessageOutlined';
import LoadingOutlined from '@ant-design/icons/LoadingOutlined';
import {
  UseMonitorChatReturn,
  MonitorGenerationResult,
  MonitorStatus,
} from '../types';
import { useChatWithMonitorMutation } from '@/apollo/client/graphql/monitors.generated';
import { GENERATION_STAGES, STAGE_DESCRIPTIONS } from '../constants';
import styled from 'styled-components';

const { TextArea } = Input;
const { Text } = Typography;

const ChatCard = styled(Card)`
  margin-top: 16px;
  border-radius: 4px;
  border: 1px solid var(--gray-4);
  background: white;

  .ant-card-body {
    padding: 16px;
  }
`;

const ChatHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
`;

const ChatHeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const ChatStatus = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--gray-6);
`;

const ChatInputContainer = styled.div`
  display: flex;
  gap: 8px;
  align-items: flex-end;
  margin-top: 12px;
`;

const StyledTextArea = styled(TextArea)`
  flex: 1;

  .ant-input {
    border-radius: 4px;
    border: 1px solid var(--gray-4);
    min-height: 60px;
  }
`;

const SendButton = styled(Button)`
  height: 40px;
  min-width: 80px;
  border-radius: 4px;
`;

const PendingRefinements = styled.div`
  margin-top: 12px;
  padding: 12px;
  background: var(--gray-2);
  border: 1px solid var(--gray-4);
  border-radius: 4px;
`;

const PendingItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 12px;
  color: var(--gray-7);
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
`;

const PendingItemHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PendingItemStatus = styled.div`
  margin-left: 16px;
  font-size: 11px;
  color: var(--gray-6);
  display: flex;
  align-items: center;
  gap: 4px;
`;

const StageIndicators = styled.div`
  display: flex;
  gap: 12px;
  margin-left: 16px;
  margin-top: 6px;
`;

const StageDot = styled.div<{ active: boolean; completed: boolean }>`
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: ${(props) =>
    props.completed
      ? 'var(--green-6)'
      : props.active
        ? 'var(--geekblue-6)'
        : 'var(--gray-4)'};
`;

const StageLabel = styled.span<{ active: boolean; completed: boolean }>`
  font-size: 10px;
  color: ${(props) =>
    props.completed
      ? 'var(--green-6)'
      : props.active
        ? 'var(--geekblue-6)'
        : 'var(--gray-6)'};
`;

interface MonitorChatInterfaceProps {
  chatHook: UseMonitorChatReturn;
  latestResult?: MonitorGenerationResult;
  threadResponse: {
    id: number;
    sql?: string;
    question?: string;
    answerDetail?: any;
    chartDetail?: any;
    breakdownDetail?: any;
  };
  monitorStatuses?: Record<string, MonitorStatus>;
}

export const MonitorChatInterface: React.FC<MonitorChatInterfaceProps> = ({
  chatHook,
  latestResult,
  threadResponse,
  monitorStatuses = {},
}) => {
  const {
    chatInput,
    setChatInput,
    isChatEnabled,
    isChatSubmitting,
    generations,
    activeTaskIds,
    setGenerations,
    setActiveTaskIds,
    setIsChatSubmitting,
  } = chatHook;

  const [chatWithMonitor] = useChatWithMonitorMutation();

  const pendingRefinements = generations.filter(
    (gen) => !gen.result?.monitor_sql && activeTaskIds.has(gen.id),
  );

  // Helper function to get current stage index for a task
  const getCurrentStage = (taskId: string): number => {
    const status = monitorStatuses[taskId];
    if (!status) return 0;

    switch (status) {
      case 'UNDERSTANDING':
        return 0;
      case 'REASONING':
        return 1;
      case 'GENERATING':
        return 2;
      case 'VALIDATING':
        return 3;
      default:
        return 0;
    }
  };

  // Helper function to get stage description
  const getStageDescription = (taskId: string) => {
    const status = monitorStatuses[taskId];
    if (!status || !STAGE_DESCRIPTIONS[status]) {
      return {
        title: 'Processing',
        subtitle: 'Processing your refinement...',
        details: 'Working on your request...',
      };
    }
    return STAGE_DESCRIPTIONS[status];
  };

  // Custom handleChatSubmit that uses the latest result instead of the initial one
  const handleChatSubmit = async (): Promise<void> => {
    if (!chatInput.trim() || !latestResult || isChatSubmitting) {
      return;
    }

    setIsChatSubmitting(true);

    try {
      // Use the latest monitor result SQL (from the most recent refinement)
      const currentSql = latestResult.monitor_sql || '';

      // Start a new chat refinement task
      const chatResult = await chatWithMonitor({
        variables: {
          data: {
            originalMessage: threadResponse?.question || '',
            existingSql: currentSql,
            chatPrompt: chatInput,
            threadData: {
              answerDetail: threadResponse?.answerDetail || {},
              chartDetail: threadResponse?.chartDetail || {},
              breakdownDetail: threadResponse?.breakdownDetail || {},
            },
            projectId: null, // Add project ID if available
            queryId: `${threadResponse.id}_chat_${Date.now()}`,
          },
        },
      });

      if (chatResult.data?.chatWithMonitor?.id) {
        const chatTaskId = chatResult.data.chatWithMonitor.id;

        // Add the chat generation to the generations array with pending status
        setGenerations((prev) => [
          ...prev,
          {
            id: chatTaskId,
            result: {} as MonitorGenerationResult, // Will be populated when complete
            timestamp: new Date(),
            chatPrompt: chatInput,
          },
        ]);

        // Add to active tasks for polling
        setActiveTaskIds((prev) => {
          if (prev.has(chatTaskId)) return prev;
          const newSet = new Set(prev);
          newSet.add(chatTaskId);
          return newSet;
        });

        // Clear the chat input
        setChatInput('');

        message.success('Chat refinement started');
      }
    } catch (error: any) {
      setIsChatSubmitting(false);
      message.error(`Chat refinement failed: ${error.message}`);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleChatSubmit();
    }
  };

  return (
    <ChatCard>
      <ChatHeader>
        <ChatHeaderLeft>
          <MessageOutlined
            style={{ color: 'var(--gray-6)', fontSize: '16px' }}
          />
          <Text strong style={{ color: 'var(--gray-9)' }}>
            Refine Monitor
          </Text>
        </ChatHeaderLeft>
        {isChatSubmitting && (
          <ChatStatus>
            <LoadingOutlined />
            <Text style={{ fontSize: '12px' }}>Processing...</Text>
          </ChatStatus>
        )}
      </ChatHeader>

      <Text style={{ color: 'var(--gray-7)', fontSize: '14px' }}>
        Describe how you'd like to modify the monitor.
      </Text>

      {/* Pending Refinements */}
      {pendingRefinements.length > 0 && (
        <PendingRefinements>
          <Text strong style={{ fontSize: '12px', color: 'var(--gray-8)' }}>
            Processing:
          </Text>
          {pendingRefinements.map((gen) => {
            const currentStage = getCurrentStage(gen.id);
            const stageDescription = getStageDescription(gen.id);

            return (
              <PendingItem key={gen.id}>
                <PendingItemHeader>
                  <LoadingOutlined style={{ color: 'var(--gray-6)' }} />
                  <span style={{ fontStyle: 'italic', color: 'var(--gray-7)' }}>
                    "{gen.chatPrompt}"
                  </span>
                </PendingItemHeader>

                <PendingItemStatus>
                  <span>{stageDescription.subtitle}</span>
                </PendingItemStatus>

                <StageIndicators>
                  {GENERATION_STAGES.map((stage, index) => (
                    <div
                      key={stage}
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        gap: '2px',
                      }}
                    >
                      <StageDot
                        active={index === currentStage}
                        completed={index < currentStage}
                      />
                      <StageLabel
                        active={index === currentStage}
                        completed={index < currentStage}
                      >
                        {stage}
                      </StageLabel>
                    </div>
                  ))}
                </StageIndicators>

                <div
                  style={{
                    marginLeft: '16px',
                    fontSize: '10px',
                    color: 'var(--gray-6)',
                  }}
                >
                  {stageDescription.details}
                </div>
              </PendingItem>
            );
          })}
        </PendingRefinements>
      )}

      {/* Chat Input */}
      <ChatInputContainer>
        <StyledTextArea
          placeholder="Describe how to modify the monitor..."
          value={chatInput}
          onChange={(e) => setChatInput(e.target.value)}
          rows={2}
          disabled={!isChatEnabled || isChatSubmitting}
          onKeyDown={handleKeyDown}
        />
        <SendButton
          type="primary"
          disabled={!isChatEnabled || !chatInput.trim() || isChatSubmitting}
          loading={isChatSubmitting}
          onClick={handleChatSubmit}
        >
          {isChatSubmitting ? 'Processing...' : 'Refine'}
        </SendButton>
      </ChatInputContainer>
    </ChatCard>
  );
};
