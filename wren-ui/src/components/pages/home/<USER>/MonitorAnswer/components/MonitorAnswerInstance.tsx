import { Typography, Tag, Alert, Badge } from '@/import/antd';
import CheckCircleOutlined from '@ant-design/icons/CheckCircleOutlined';
import ExclamationCircleOutlined from '@ant-design/icons/ExclamationCircleOutlined';
import DownOutlined from '@ant-design/icons/DownOutlined';
import RightOutlined from '@ant-design/icons/RightOutlined';
import MessageOutlined from '@ant-design/icons/MessageOutlined';
import { MonitorSQLSection } from './MonitorSQLSection';
import { MonitorInsights } from './MonitorInsights';
import { useSQLPreview } from '../hooks/useSQLPreview';
import { MonitorGeneration } from '../types';
import styled from 'styled-components';

const { Text } = Typography;

const InstanceCard = styled.div<{ isExpanded: boolean; isLatest: boolean }>`
  border: 1px solid var(--gray-4);
  border-radius: 12px;
  margin-bottom: 16px;
  overflow: hidden;
  transition: all 0.3s ease;

  ${(props) =>
    props.isLatest &&
    `
    border-color: var(--blue-5);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  `}

  ${(props) =>
    !props.isExpanded &&
    `
    &:hover {
      border-color: var(--blue-4);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  `}
`;

const InstanceHeader = styled.div<{ isExpanded: boolean }>`
  padding: 16px 20px;
  background: ${(props) =>
    props.isExpanded ? 'var(--blue-1)' : 'var(--gray-1)'};
  border-bottom: ${(props) =>
    props.isExpanded ? '1px solid var(--blue-3)' : 'none'};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;

  &:hover {
    background: ${(props) =>
      props.isExpanded ? 'var(--blue-1)' : 'var(--gray-2)'};
  }
`;

const InstanceHeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const InstanceContent = styled.div`
  padding: 24px;
`;

const ChatPromptBadge = styled.div`
  background: var(--blue-1);
  border: 1px solid var(--blue-3);
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 16px;
  font-size: 14px;
  color: var(--gray-8);

  .prompt-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--blue-7);
    margin-bottom: 4px;
  }
`;

interface MonitorAnswerInstanceProps {
  instance: MonitorGeneration;
  isExpanded: boolean;
  isLatest: boolean;
  instanceNumber: number;
  onToggleExpanded: () => void;
  threadResponse: {
    id: number;
    sql?: string;
    question?: string;
    answerDetail?: any;
    chartDetail?: any;
    breakdownDetail?: any;
  };
  onExpressionChange?: (expression: string, result: boolean | null) => void;
}

export const MonitorAnswerInstance: React.FC<MonitorAnswerInstanceProps> = ({
  instance,
  isExpanded,
  isLatest,
  instanceNumber,
  onToggleExpanded,
  onExpressionChange,
}) => {
  const sqlPreview = useSQLPreview({ monitorResult: instance.result });
  const isChatRefinement = Boolean(instance.chatPrompt);

  const getInstanceTitle = () => {
    if (isChatRefinement) {
      return `Refinement #${instanceNumber - 1}`;
    }
    return 'Initial Monitor';
  };

  const getInstanceSubtitle = () => {
    if (isChatRefinement && instance.chatPrompt) {
      return instance.chatPrompt.length > 60
        ? `${instance.chatPrompt.substring(0, 60)}...`
        : instance.chatPrompt;
    }
    return 'AI-generated monitoring query';
  };

  return (
    <InstanceCard isExpanded={isExpanded} isLatest={isLatest}>
      <InstanceHeader isExpanded={isExpanded} onClick={onToggleExpanded}>
        <InstanceHeaderLeft>
          {isExpanded ? <DownOutlined /> : <RightOutlined />}

          <div>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <Text strong style={{ fontSize: '16px' }}>
                {getInstanceTitle()}
              </Text>
              {isLatest && <Badge status="processing" text="Latest" />}
              {isChatRefinement && (
                <MessageOutlined style={{ color: 'var(--blue-6)' }} />
              )}
            </div>
            <Text type="secondary" style={{ fontSize: '14px' }}>
              {getInstanceSubtitle()}
            </Text>
          </div>
        </InstanceHeaderLeft>

        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          {instance.result?.validation_results?.execution_successful ? (
            <Tag icon={<CheckCircleOutlined />} color="success">
              Validated
            </Tag>
          ) : (
            <Tag icon={<ExclamationCircleOutlined />} color="error">
              Failed
            </Tag>
          )}
        </div>
      </InstanceHeader>

      {isExpanded && (
        <InstanceContent>
          {/* Chat Prompt Display */}
          {isChatRefinement && instance.chatPrompt && (
            <ChatPromptBadge>
              <div className="prompt-label">Refinement Request:</div>
              {instance.chatPrompt}
            </ChatPromptBadge>
          )}

          {/* SQL Section */}
          <MonitorSQLSection
            monitorResult={instance.result}
            showSQL={sqlPreview.showSQL}
            setShowSQL={sqlPreview.setShowSQL}
            previewSqlResult={sqlPreview.previewSqlResult}
            onTestSQL={sqlPreview.handleTestSQL}
            onExpressionChange={onExpressionChange}
          />

          {/* Validation Error Details */}
          {instance.result?.validation_results?.execution_error && (
            <Alert
              type="error"
              message="SQL Validation Error"
              description={instance.result.validation_results.execution_error}
              showIcon
              style={{ marginTop: 16 }}
            />
          )}

          {/* AI Insights */}
          <MonitorInsights monitorResult={instance.result} />
        </InstanceContent>
      )}
    </InstanceCard>
  );
};
