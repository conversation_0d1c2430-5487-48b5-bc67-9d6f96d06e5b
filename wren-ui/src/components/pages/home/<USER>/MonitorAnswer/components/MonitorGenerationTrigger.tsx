import { Button } from '@/import/antd';
import BulbOutlined from '@ant-design/icons/BulbOutlined';
import { MonitorContainer, GenerationCard } from '../styles';

interface MonitorGenerationTriggerProps {
  onGenerateMonitor: () => void;
}

export const MonitorGenerationTrigger: React.FC<
  MonitorGenerationTriggerProps
> = ({ onGenerateMonitor }) => {
  return (
    <MonitorContainer>
      <GenerationCard>
        <div className="generation-content">
          <div className="generation-title">Create Monitor</div>
          <div className="generation-subtitle">
            Create a monitoring system from your analysis.
          </div>
          <Button
            type="primary"
            icon={<BulbOutlined />}
            onClick={onGenerateMonitor}
          >
            Generate Monitor
          </Button>
        </div>
      </GenerationCard>
    </MonitorContainer>
  );
};
