import { Button, Input, Typography } from '@/import/antd';
import MessageOutlined from '@ant-design/icons/MessageOutlined';
import LoadingOutlined from '@ant-design/icons/LoadingOutlined';
import SendOutlined from '@ant-design/icons/SendOutlined';
import { ChatSection } from '../styles';
import { MonitorGeneration } from '../types';

const { TextArea } = Input;
const { Text, Title } = Typography;

interface MonitorChatProps {
  chatInput: string;
  setChatInput: (value: string) => void;
  isChatEnabled: boolean;
  isChatSubmitting: boolean;
  generations: MonitorGeneration[];
  activeTaskIds: Set<string>;
  onChatSubmit: () => void;
}

export const MonitorChat: React.FC<MonitorChatProps> = ({
  chatInput,
  setChatInput,
  isChatEnabled,
  isChatSubmitting,
  generations,
  activeTaskIds,
  onChatSubmit,
}) => {
  return (
    <ChatSection
      className={`${isChatEnabled ? 'enabled' : ''} ${
        isChatSubmitting ? 'submitting' : ''
      }`}
    >
      {!isChatEnabled && <div className="coming-soon-overlay">Coming Soon</div>}
      <div className="chat-header">
        <div className="chat-header-left">
          <MessageOutlined style={{ color: 'var(--geekblue-6)' }} />
          <Title className="chat-title" level={5}>
            Chat with Monitor
          </Title>
        </div>
        {isChatSubmitting && (
          <div className="chat-status">
            <LoadingOutlined />
            Processing...
          </div>
        )}
      </div>
      <div className="chat-subtitle">
        Refine your monitor SQL by chatting with AI about specific changes or
        improvements you'd like to make.
      </div>
      {/* Show pending generations */}
      {generations.some(
        (gen) => !gen.result?.monitor_sql && activeTaskIds.has(gen.id),
      ) && (
        <div className="pending-generations">
          <Text strong style={{ fontSize: '13px', color: 'var(--blue-7)' }}>
            Processing Refinements:
          </Text>
          {generations
            .filter(
              (gen) => !gen.result?.monitor_sql && activeTaskIds.has(gen.id),
            )
            .map((gen) => (
              <div key={gen.id} className="pending-item">
                <LoadingOutlined style={{ color: 'var(--geekblue-6)' }} />
                <span className="pending-prompt">"{gen.chatPrompt}"</span>
              </div>
            ))}
        </div>
      )}
      <div className="chat-input-container">
        <TextArea
          className="chat-input"
          placeholder="e.g., Add a filter for specific regions, Change the time window to last 30 days, Include percentage change calculations..."
          value={chatInput}
          onChange={(e) => setChatInput(e.target.value)}
          rows={2}
          disabled={!isChatEnabled || isChatSubmitting}
          onPressEnter={(e) => {
            if (e.shiftKey) return;
            e.preventDefault();
            onChatSubmit();
          }}
        />
        <Button
          type="primary"
          className="chat-send-button"
          disabled={!isChatEnabled || !chatInput.trim() || isChatSubmitting}
          loading={isChatSubmitting}
          onClick={onChatSubmit}
        >
          {isChatSubmitting ? (
            <span className="loading-text">
              <LoadingOutlined />
              Refining...
            </span>
          ) : (
            <>
              <SendOutlined />
              Send
            </>
          )}
        </Button>
      </div>
    </ChatSection>
  );
};
