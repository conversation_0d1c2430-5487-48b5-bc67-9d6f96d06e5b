import { Typography } from '@/import/antd';
import BulbOutlined from '@ant-design/icons/BulbOutlined';
import { InsightsList } from '../styles';
import { MonitorGenerationResult } from '../types';

const { Title } = Typography;

interface MonitorInsightsProps {
  monitorResult?: MonitorGenerationResult;
}

export const MonitorInsights: React.FC<MonitorInsightsProps> = ({
  monitorResult,
}) => {
  if (
    !monitorResult?.reasoning_steps ||
    monitorResult.reasoning_steps.length === 0
  ) {
    return null;
  }

  return (
    <InsightsList>
      <Title level={5} style={{ margin: '0 0 12px 0' }}>
        AI Insights
      </Title>
      {monitorResult.reasoning_steps.map((step, index) => (
        <div key={index} className="insight-item">
          <BulbOutlined className="insight-icon" />
          <span className="insight-text">{step}</span>
        </div>
      ))}
    </InsightsList>
  );
};
