import styled from 'styled-components';
import { Card } from '@/import/antd';

export const MonitorContainer = styled.div`
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

export const GenerationCard = styled(Card)`
  text-align: center;

  .ant-card-body {
    padding: 24px;
  }

  .generation-content {
    max-width: 400px;
    margin: 0 auto;
  }

  .generation-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-9);
    margin-bottom: 8px;
  }

  .generation-subtitle {
    color: var(--gray-7);
    margin-bottom: 20px;
    line-height: 1.5;
  }

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    margin-top: 16px;
  }

  .loading-stages {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 16px;
  }

  .stage-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }

  .stage-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--gray-4);

    &.active {
      background: var(--geekblue-6);
    }

    &.completed {
      background: var(--green-6);
    }
  }

  .stage-label {
    font-size: 11px;
    color: var(--gray-6);

    &.active {
      color: var(--geekblue-6);
      font-weight: 500;
    }

    &.completed {
      color: var(--green-6);
    }
  }
`;

export const ResultCard = styled(Card)`
  .ant-card-body {
    padding: 16px;
  }

  .result-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .status-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
  }

  .actions {
    display: flex;
    gap: 6px;
  }
`;

export const SQLSection = styled.div`
  margin-top: 16px;

  .sql-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
  }

  .sql-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-9);
    margin: 0;
  }

  .view-toggle {
    margin-left: auto;
  }
`;

export const InsightsList = styled.div`
  margin-top: 12px;

  .insight-item {
    display: flex;
    align-items: flex-start;
    gap: 6px;
    margin-bottom: 6px;
    padding: 6px 0;

    .insight-icon {
      color: var(--gray-6);
      margin-top: 2px;
    }

    .insight-text {
      flex: 1;
      font-size: 14px;
      line-height: 1.4;
      color: var(--gray-8);
    }
  }
`;

export const ChatSection = styled.div`
  margin-top: 16px;
  padding: 16px;
  background: white;
  border-radius: 4px;
  border: 1px solid var(--gray-4);

  &.disabled {
    opacity: 0.6;
  }

  .chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
  }

  .chat-header-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .chat-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-9);
    margin: 0;
  }

  .chat-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: var(--gray-6);
  }

  .chat-subtitle {
    font-size: 14px;
    color: var(--gray-7);
    margin-bottom: 12px;
    line-height: 1.5;
  }

  .chat-input-container {
    display: flex;
    gap: 8px;
    align-items: flex-end;
  }

  .chat-input {
    flex: 1;

    .ant-input {
      border-radius: 4px;
      border: 1px solid var(--gray-4);
    }
  }

  .chat-send-button {
    height: 40px;
    min-width: 80px;
    border-radius: 4px;
  }

  .pending-generations {
    margin-top: 12px;
    padding: 12px;
    background: var(--gray-2);
    border: 1px solid var(--gray-4);
    border-radius: 4px;
  }

  .pending-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--gray-7);
    margin-bottom: 6px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .pending-prompt {
    flex: 1;
    font-style: italic;
    color: var(--gray-7);
  }
`;

export const GenerationHistorySection = styled.div`
  margin-top: 16px;

  .history-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
  }

  .history-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-9);
    margin: 0;
  }

  .generation-item {
    border: 1px solid var(--gray-4);
    border-radius: 4px;
    margin-bottom: 8px;
    overflow: hidden;

    &.current {
      border-color: var(--geekblue-6);
    }
  }

  .generation-header {
    padding: 8px 12px;
    background: var(--gray-2);
    border-bottom: 1px solid var(--gray-4);
    display: flex;
    align-items: center;
    justify-content: space-between;

    .current & {
      background: var(--gray-3);
    }
  }

  .generation-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .generation-number {
    font-weight: 600;
    color: var(--gray-9);
  }

  .generation-type {
    font-size: 12px;
    color: var(--gray-7);
  }

  .generation-time {
    font-size: 12px;
    color: var(--gray-6);
  }

  .generation-content {
    padding: 12px;
  }

  .chat-prompt {
    background: var(--gray-2);
    border: 1px solid var(--gray-4);
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 12px;
    font-size: 14px;
    color: var(--gray-8);

    .prompt-label {
      font-size: 12px;
      font-weight: 500;
      color: var(--gray-8);
      margin-bottom: 4px;
    }
  }

  .sql-preview {
    background: var(--gray-2);
    border: 1px solid var(--gray-4);
    border-radius: 4px;
    padding: 12px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 12px;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
    line-height: 1.4;
  }
`;
