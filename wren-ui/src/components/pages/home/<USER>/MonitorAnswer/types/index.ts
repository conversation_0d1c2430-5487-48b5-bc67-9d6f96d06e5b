export interface MonitorGenerationResult {
  monitor_sql?: string;
  reasoning_steps?: string[];
  template_suggestions?: {
    template: string;
    description: string;
    reasoning: string;
    fields: string[];
    conditions?: string[];
    priority?: number;
  }[];
  validation_results?: {
    execution_successful: boolean;
    execution_error?: string;
    sample_result?: string;
  };
}

export interface MonitorGeneration {
  id: string;
  result: MonitorGenerationResult;
  timestamp: Date;
  chatPrompt?: string;
  // Persistence-related fields
  persistenceId?: number;
  status?: MonitorStatus;
  expression?: string | null;
  expressionResult?: boolean | null;
  expressionBuilderState?: any;
  keyInsights?: string[];
  uiState?: any;
}

export interface StageDescription {
  title: string;
  subtitle: string;
  details: string;
}

export type MonitorStatus =
  | 'UNDERSTANDING'
  | 'REASONING'
  | 'GENERATING'
  | 'VALIDATING'
  | 'FINISHED'
  | 'FAILED'
  | 'PENDING'
  | 'STOPPED';

export interface MonitorAnswerProps {
  threadResponse: {
    id: number;
    sql?: string;
    question?: string;
    answerDetail?: any;
    chartDetail?: any;
    breakdownDetail?: any;
  };
}

export interface UseMonitorGenerationReturn {
  monitorTaskId: string | null;
  monitorStatus: MonitorStatus | undefined;
  monitorFinished: boolean;
  monitorFailed: boolean;
  monitorInProgress: boolean;
  monitorResult: MonitorGenerationResult | undefined;
  monitorResultError: any;
  getCurrentStage: () => number;
  handleTestSQL: () => Promise<void>;
  previewSqlResult: any;
}

export interface UseMonitorChatReturn {
  chatInput: string;
  setChatInput: (value: string) => void;
  isChatEnabled: boolean;
  isChatSubmitting: boolean;
  generations: MonitorGeneration[];
  showGenerationHistory: boolean;
  setShowGenerationHistory: (show: boolean) => void;
  activeTaskIds: Set<string>;
  handleChatSubmit: () => Promise<void>;
  formatTimestamp: (timestamp: Date) => string;
  setGenerations: React.Dispatch<React.SetStateAction<MonitorGeneration[]>>;
  setActiveTaskIds: React.Dispatch<React.SetStateAction<Set<string>>>;
  setIsChatSubmitting: (submitting: boolean) => void;
  monitorStatuses: Record<string, MonitorStatus>;
}

export interface UseSQLPreviewReturn {
  showSQL: boolean;
  setShowSQL: (show: boolean) => void;
  previewSqlResult: any;
  handleTestSQL: () => Promise<void>;
}
