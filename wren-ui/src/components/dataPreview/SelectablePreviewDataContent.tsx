import { useMemo } from 'react';
import { Table, TableColumnProps } from 'antd';
import { isString } from 'lodash';
import styled from 'styled-components';

const FONT_SIZE = 16;
const BASIC_COLUMN_WIDTH = 100;

// Styled table with row numbers
const StyledTable = styled(Table)`
  .ant-table-thead > tr > th:first-child {
    background: var(--gray-2);
    font-weight: 600;
    text-align: center;
    width: 40px;
    padding: 8px 4px;
  }

  .ant-table-tbody > tr > td:first-child {
    background: var(--gray-1);
    font-weight: 500;
    text-align: center;
    width: 40px;
    padding: 8px 4px;
    border-right: 1px solid var(--gray-4);
  }

  .ant-table-tbody > tr:hover > td:first-child {
    background: var(--gray-2);
  }
`;

type TableColumn = TableColumnProps<any> & { titleText?: string };

interface Props {
  columns: TableColumn[];
  data: Array<any[]>;
  loading: boolean;
  locale?: { emptyText: React.ReactNode };
}

const getValueByValueType = (value: any) =>
  ['boolean', 'object'].includes(typeof value) ? JSON.stringify(value) : value;

const convertResultData = (data: Array<any>, columns: TableColumn[]) => {
  return data.map((datum: Array<any>, index: number) => {
    const obj: any = {};
    // should have a unique "key" prop.
    obj['key'] = index;
    obj['rowNumber'] = index + 1; // Add row number

    datum.forEach((value, colIndex) => {
      const column = columns[colIndex];
      if (column && column.dataIndex) {
        obj[column.dataIndex as string] = getValueByValueType(value);
      }
    });

    return obj;
  });
};

export default function SelectablePreviewDataContent(props: Props) {
  const { columns = [], data = [], loading, locale } = props;
  const hasColumns = !!columns.length;

  const dynamicWidth = useMemo(() => {
    const contentWidth = columns.reduce((result, column) => {
      const width = isString(column.titleText || column.title)
        ? (column.titleText || (column.title as string)).length * FONT_SIZE
        : BASIC_COLUMN_WIDTH;
      return result + width;
    }, 0);
    return contentWidth + 40; // Add width for row number column
  }, [columns]);

  const tableColumns = useMemo(() => {
    // Add row number column
    const rowNumberColumn: TableColumn = {
      title: '#',
      dataIndex: 'rowNumber',
      key: 'rowNumber',
      width: 40,
      fixed: 'left',
      render: (value) => (
        <span style={{ color: 'var(--gray-6)', fontSize: '12px' }}>
          {value}
        </span>
      ),
    };

    const dataColumns = columns.map((column) => ({
      ...column,
      ellipsis: true,
    }));

    return [rowNumberColumn, ...dataColumns];
  }, [columns]);

  const dataSource = useMemo(
    () => convertResultData(data, columns),
    [data, columns],
  );

  return (
    <StyledTable
      className={`ph-no-capture ${hasColumns ? 'ant-table-has-header' : ''}`}
      showHeader={hasColumns}
      dataSource={dataSource}
      columns={tableColumns}
      pagination={false}
      size="small"
      scroll={{ y: 280, x: dynamicWidth }}
      loading={loading}
      locale={locale}
      bordered
    />
  );
}
