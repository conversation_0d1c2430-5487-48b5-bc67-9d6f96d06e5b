import { memo, useMemo, useState, useCallback } from 'react';
import { <PERSON>ert, Typography, Button, Input, Card, Space, Tag } from 'antd';
import { ApolloError } from '@apollo/client';
import styled from 'styled-components';
import { getColumnTypeIcon } from '@/utils/columnType';
import { parseGraphQLError } from '@/utils/errorHandler';
import SelectablePreviewDataContent from './SelectablePreviewDataContent';

const { Text } = Typography;

const StyledCell = styled.div`
  position: relative;

  .copy-icon {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    opacity: 0;
    transition: opacity 0.3s;
  }

  .ant-typography-copy {
    margin: -4px;
  }

  &:hover .copy-icon {
    opacity: 1;
  }
`;

const ExpressionBuilder = styled(Card)`
  margin-top: 16px;

  .expression-input {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
  }

  .referenced-cells {
    margin-top: 8px;
  }

  .preview-result {
    margin-top: 12px;
    padding: 8px 12px;
    background: var(--gray-1);
    border-radius: 6px;
    font-weight: 500;

    &.result-true {
      background: var(--green-1);
      color: var(--green-7);
    }

    &.result-false {
      background: var(--red-1);
      color: var(--red-7);
    }

    &.result-error {
      background: var(--orange-1);
      color: var(--orange-7);
    }
  }
`;

const ColumnTitle = memo(
  (props: { name: string; type: any; columnIndex: number }) => {
    const { name, type, columnIndex } = props;
    const columnTypeIcon = getColumnTypeIcon({ type }, { title: type });
    const excelColumn = String.fromCharCode(65 + columnIndex); // A, B, C, etc.

    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Text strong style={{ fontSize: '12px', color: 'var(--gray-6)' }}>
          {excelColumn}
        </Text>
        <div
          style={{ display: 'flex', alignItems: 'center', marginTop: '4px' }}
        >
          {columnTypeIcon}
          <Text title={name} className="ml-1">
            {name}
          </Text>
        </div>
      </div>
    );
  },
);

const ColumnContext = memo(
  (props: {
    text: string;
    copyable: boolean;
    isSelected: boolean;
    isReferenced: boolean;
    onClick: () => void;
  }) => {
    const { text, copyable, isSelected, isReferenced, onClick } = props;

    const cellStyle = {
      cursor: 'pointer',
      padding: '4px 8px',
      borderRadius: '4px',
      border: isSelected ? '2px solid var(--blue-5)' : '1px solid transparent',
      backgroundColor: isReferenced
        ? 'var(--blue-1)'
        : isSelected
          ? 'var(--blue-2)'
          : 'transparent',
      transition: 'all 0.2s ease',
    };

    return (
      <StyledCell className="text-truncate" style={cellStyle} onClick={onClick}>
        <span title={text} className="text text-container">
          {text}
        </span>
        {copyable && (
          <Button size="small" className="copy-icon">
            <Text copyable={{ text, tooltips: false }} className="gray-8" />
          </Button>
        )}
      </StyledCell>
    );
  },
);

// Excel column helper
const getExcelColumn = (index: number): string => {
  let result = '';
  while (index >= 0) {
    result = String.fromCharCode(65 + (index % 26)) + result;
    index = Math.floor(index / 26) - 1;
  }
  return result;
};

// Cell reference helper
const getCellReference = (rowIndex: number, colIndex: number): string => {
  return `${getExcelColumn(colIndex)}${rowIndex + 1}`;
};

// Expression parser and evaluator
const parseExpression = (
  expression: string,
  cellValues: Map<string, number>,
) => {
  try {
    // Replace cell references with actual values
    let processedExpression = expression;

    // Find all cell references (A1, B2, etc.)
    const cellReferenceRegex = /[A-Z]+\d+/g;
    const references = expression.match(cellReferenceRegex) || [];

    for (const ref of references) {
      const value = cellValues.get(ref);
      if (value !== undefined) {
        processedExpression = processedExpression.replace(
          new RegExp(ref, 'g'),
          value.toString(),
        );
      } else {
        throw new Error(`Cell ${ref} not found or contains non-numeric value`);
      }
    }

    // Basic expression evaluation (safe eval alternative)
    // Only allow numbers, operators, parentheses, and basic comparisons
    const safeExpressionRegex = /^[\d\s+\-*/.()>=<!&|]+$/;
    if (!safeExpressionRegex.test(processedExpression)) {
      throw new Error('Invalid expression syntax');
    }

    // Replace logical operators
    processedExpression = processedExpression
      .replace(/\s+AND\s+/gi, ' && ')
      .replace(/\s+OR\s+/gi, ' || ')
      .replace(/=/g, '==');

    // Evaluate the expression
    const result = Function(`"use strict"; return (${processedExpression})`)();

    return { result, error: null, references };
  } catch (error) {
    return { result: null, error: error.message, references: [] };
  }
};

const getPreviewColumns = (
  cols,
  { copyable, selectedCell, referencedCells, onCellClick },
) =>
  cols.map(({ name, type }: Record<string, any>, colIndex: number) => {
    return {
      dataIndex: name,
      titleText: name,
      key: name,
      ellipsis: true,
      title: <ColumnTitle name={name} type={type} columnIndex={colIndex} />,
      render: (text, _record, rowIndex) => {
        const cellRef = getCellReference(rowIndex, colIndex);
        const isSelected = selectedCell === cellRef;
        const isReferenced = referencedCells.includes(cellRef);

        return (
          <ColumnContext
            text={text}
            copyable={copyable}
            isSelected={isSelected}
            isReferenced={isReferenced}
            onClick={() => onCellClick(cellRef, text)}
          />
        );
      },
      onCell: () => ({ style: { lineHeight: '32px', padding: '4px 8px' } }),
    };
  });

interface Props {
  previewData?: {
    data: Array<Array<any>>;
    columns: Array<{
      name: string;
      type: string;
    }>;
  };
  loading: boolean;
  error?: ApolloError;
  locale?: { emptyText: React.ReactNode };
  copyable?: boolean;
  onExpressionChange?: (expression: string, result: boolean | null) => void;
}

export default function SelectablePreviewData(props: Props) {
  const {
    previewData,
    loading,
    error,
    locale,
    copyable = true,
    onExpressionChange,
  } = props;
  const [selectedCell, setSelectedCell] = useState<string | null>(null);
  const [expression, setExpression] = useState('');
  const [expressionResult, setExpressionResult] = useState<{
    result: boolean | null;
    error: string | null;
    references: string[];
  }>({ result: null, error: null, references: [] });

  // Build cell values map for expression evaluation
  const cellValues = useMemo(() => {
    const values = new Map<string, number>();
    if (previewData?.data && previewData?.columns) {
      previewData.data.forEach((row, rowIndex) => {
        row.forEach((cellValue, colIndex) => {
          const cellRef = getCellReference(rowIndex, colIndex);
          const numValue = parseFloat(cellValue);
          if (!isNaN(numValue)) {
            values.set(cellRef, numValue);
          }
        });
      });
    }
    return values;
  }, [previewData]);

  // Handle cell click
  const handleCellClick = useCallback((cellRef: string, _cellValue: any) => {
    setSelectedCell(cellRef);
    // Auto-fill expression input with cell reference
    setExpression((prev) => prev + cellRef);
  }, []);

  // Handle expression change
  const handleExpressionChange = useCallback(
    (value: string) => {
      setExpression(value);

      if (value.trim()) {
        const result = parseExpression(value, cellValues);
        setExpressionResult(result);
        onExpressionChange?.(value, result.result);
      } else {
        setExpressionResult({ result: null, error: null, references: [] });
        onExpressionChange?.('', null);
      }
    },
    [cellValues, onExpressionChange],
  );

  const columns = useMemo(
    () =>
      previewData?.columns &&
      getPreviewColumns(previewData.columns, {
        copyable,
        selectedCell,
        referencedCells: expressionResult.references,
        onCellClick: handleCellClick,
      }),
    [
      previewData?.columns,
      copyable,
      selectedCell,
      expressionResult.references,
      handleCellClick,
    ],
  );

  const hasErrorMessage = error && error.message;
  if (!loading && hasErrorMessage) {
    const { message, shortMessage } = parseGraphQLError(error);

    return (
      <Alert
        message={shortMessage}
        description={message}
        type="error"
        showIcon
      />
    );
  }

  return (
    <div>
      <SelectablePreviewDataContent
        columns={columns}
        data={previewData?.data || []}
        loading={loading}
        locale={locale}
      />

      {previewData?.data && previewData?.data.length > 0 && (
        <ExpressionBuilder title="Expression Builder" size="small">
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>Monitor Expression:</Text>
              <Input
                className="expression-input"
                value={expression}
                onChange={(e) => handleExpressionChange(e.target.value)}
                placeholder="Enter expression (e.g., A1 > 50 OR B2 < 10)"
                size="large"
              />
            </div>

            {expressionResult.references.length > 0 && (
              <div className="referenced-cells">
                <Text style={{ fontSize: '12px', color: 'var(--gray-6)' }}>
                  Referenced cells:
                </Text>
                <Space wrap style={{ marginLeft: '8px' }}>
                  {expressionResult.references.map((ref) => (
                    <Tag key={ref} color="blue">
                      {ref}: {cellValues.get(ref) || 'N/A'}
                    </Tag>
                  ))}
                </Space>
              </div>
            )}

            {expression && (
              <div
                className={`preview-result ${
                  expressionResult.error
                    ? 'result-error'
                    : expressionResult.result === true
                      ? 'result-true'
                      : expressionResult.result === false
                        ? 'result-false'
                        : ''
                }`}
              >
                {expressionResult.error ? (
                  <span>Error: {expressionResult.error}</span>
                ) : expressionResult.result !== null ? (
                  <span>
                    Result: {expressionResult.result ? 'TRUE' : 'FALSE'}
                  </span>
                ) : (
                  <span>Enter a valid expression</span>
                )}
              </div>
            )}
          </Space>
        </ExpressionBuilder>
      )}
    </div>
  );
}
