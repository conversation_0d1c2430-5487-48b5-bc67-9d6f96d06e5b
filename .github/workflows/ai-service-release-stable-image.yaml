name: AI Service Release stable image

on:
  workflow_dispatch:
    inputs:
      version:
        description: Give a version for this release
        type: string
        required: true

env:
  WREN_AI_SERVICE_IMAGE: ghcr.io/canner/wren-ai-service

defaults:
  run:
    working-directory: wren-ai-service

jobs:
  upgrade-ai-service-version:
    runs-on: ubuntu-latest
    steps:
      - name: Generate a token
        id: generate-token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ vars.CI_APP_ID }}
          private-key: ${{ secrets.CI_APP_PRIVATE_KEY }}
      - uses: actions/checkout@v4
        with:
          token: ${{ steps.generate-token.outputs.token }}
          fetch-depth: 0
      - name: Set up Git
        run: |
          git config --global user.name "wren-ai[bot]"
          git config --global user.email "<EMAIL>"
      - name: Set up Python 3.12.0
        uses: actions/setup-python@v4
        with:
          python-version: 3.12.0
      - name: Install Poetry
        uses: abatilo/actions-poetry@v2
        with:
          poetry-version: 1.8.3
      - name: Generate and Save Change Log
        id: changelog
        run: |
          echo "Generating change log..."
          PREVIOUS_VERSION=release/ai-service/$(poetry version -s)
          echo "Previous version: $PREVIOUS_VERSION"

          # Get the change log from the previous version to the current HEAD
          # If there is no change log, the command will return an error, so we use || true to ignore the error
          CHANGE_LOG=$(git log --pretty=format:"%s" $PREVIOUS_VERSION..HEAD | grep wren-ai-service || true)

          # Separate the change log into categories
          FEATURES=$(echo "$CHANGE_LOG" | grep "^feat" | sed 's/^/- /')
          FIXES_AND_CHORES=$(echo "$CHANGE_LOG" | grep -E "^(fix|chore)" | sed 's/^/- /')

          # Create the full change log
          FULL_CHANGE_LOG="\nChangelog for the version\n"
          if [ -n "$FEATURES" ]; then
            FULL_CHANGE_LOG+="\nFeature and Enhancement\n$FEATURES"
          fi
          if [ -n "$FIXES_AND_CHORES" ]; then
            FULL_CHANGE_LOG+="\n\nFixes and Chores\n$FIXES_AND_CHORES"
          fi

          {
            echo "CHANGE_LOG<<EOF"
            echo -e "$FULL_CHANGE_LOG"
            echo EOF
          } >> $GITHUB_ENV
      - name: Upgrade AI Service version
        run: |
          version=${{ github.event.inputs.version }}
          poetry version --next-phase $version
          git add pyproject.toml
          git commit -m "Upgrade AI Service version to $version"
          git push
          git tag -a "release/ai-service/$version" -m "${{ env.CHANGE_LOG }}"
          git push origin "release/ai-service/$version"
  build-image:
    needs: upgrade-ai-service-version
    strategy:
      fail-fast: false
      matrix:
        arch:
          - runner: ubuntu-latest
            platform: linux/amd64
          - runner: linux_arm64_runner
            platform: linux/arm64
    runs-on: ${{ matrix.arch.runner }}
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Prepare platform
        run: |
          platform=${{ matrix.arch.platform }}
          echo "PLATFORM_PAIR=${platform//\//-}" >> $GITHUB_ENV
      - name: Build and push by digest
        id: build
        uses: docker/build-push-action@v6
        with:
          platforms: ${{ matrix.arch.platform }}
          labels: ${{ env.WREN_AI_SERVICE_IMAGE }}
          context: ./wren-ai-service
          file: ./wren-ai-service/docker/Dockerfile
          outputs: type=image,name=${{ env.WREN_AI_SERVICE_IMAGE }},push-by-digest=true,name-canonical=true,push=true
      - name: Export digest
        run: |
          mkdir -p /tmp/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "/tmp/digests/${digest#sha256:}"
      - name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-${{ env.PLATFORM_PAIR }}
          path: /tmp/digests/*
          if-no-files-found: error
          retention-days: 1
  merge:
    runs-on: ubuntu-latest
    needs: [ build-image ]
    steps:
      - name: Download digests
        uses: actions/download-artifact@v4
        with:
          path: /tmp/digests
          pattern: digests-*
          merge-multiple: true
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.WREN_AI_SERVICE_IMAGE }}
          tags: |
            type=raw,${{ github.event.inputs.version }}
            type=raw,latest
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Create manifest list and push
        working-directory: /tmp/digests
        run: |
          TAGS=$(echo "${{ steps.meta.outputs.tags }}" | awk '{printf "--tag %s ", $0}')
          docker buildx imagetools create $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
            $(printf '${{ env.WREN_AI_SERVICE_IMAGE }}@sha256:%s ' *) \
            $TAGS
