apiVersion: apps/v1
kind: Deployment
metadata:
  name: wren-ui-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: wren-ui
  template:
    metadata:
      labels:
        app: wren-ui
    spec:
      containers:

        - name: wren-ui
          image: ghcr.io/canner/wren-ui:0.5.6
          env:
            - name: DB_TYPE
              valueFrom:
                configMapKeyRef:
                  name: wren-config
                  key: WREN_UI_DB_TYPE
            - name: WREN_ENGINE_ENDPOINT
              valueFrom:
                configMapKeyRef:
                  name: wren-config
                  key: WREN_ENGINE_ENDPOINT
            - name: <PERSON>EN_AI_ENDPOINT
              valueFrom:
                configMapKeyRef:
                  name: wren-config
                  key: WREN_AI_ENDPOINT
            - name: GENERATION_MODEL
              valueFrom:
                configMapKeyRef:
                  name: wren-config
                  key: GENERATION_MODEL
            - name: PG_URL
              valueFrom:
                secretKeyRef:
                  name: wrenai-secrets
                  key: PG_URL
            # telemetry
            - name: WREN_ENGINE_PORT
              valueFrom:
                configMapKeyRef:
                  name: wren-config
                  key: WREN_ENGINE_PORT
            - name: WREN_AI_SERVICE_VERSION
              valueFrom:
                configMapKeyRef:
                  name: wren-config
                  key: WREN_AI_SERVICE_VERSION
            - name: WREN_UI_VERSION
              valueFrom:
                configMapKeyRef:
                  name: wren-config
                  key: WREN_UI_VERSION
            - name: WREN_ENGINE_VERSION
              valueFrom:
                configMapKeyRef:
                  name: wren-config
                  key: WREN_ENGINE_VERSION
            - name: USER_UUID
              valueFrom:
                secretKeyRef:
                  name: wrenai-secrets
                  key: USER_UUID
            - name: POSTHOG_API_KEY
              valueFrom:
                secretKeyRef:
                  name: wrenai-secrets
                  key: POSTHOG_API_KEY
            - name: POSTHOG_HOST
              valueFrom:
                secretKeyRef:
                  name: wrenai-secrets
                  key: POSTHOG_HOST
            - name: TELEMETRY_ENABLED
              valueFrom: 
                configMapKeyRef:
                  name: wren-config
                  key: TELEMETRY_ENABLED
            # client side
            - name: NEXT_PUBLIC_USER_UUID
              valueFrom:
                secretKeyRef:
                  name: wrenai-secrets
                  key: USER_UUID
            - name: NEXT_PUBLIC_POSTHOG_API_KEY
              valueFrom:
                secretKeyRef:
                  name: wrenai-secrets
                  key: POSTHOG_API_KEY
            - name: NEXT_PUBLIC_POSTHOG_HOST
              valueFrom:
                secretKeyRef:
                  name: wrenai-secrets
                  key: POSTHOG_HOST
            - name: NEXT_PUBLIC_TELEMETRY_ENABLED
              valueFrom: 
                configMapKeyRef:
                  name: wren-config
                  key: TELEMETRY_ENABLED
            # configs
            - name: WREN_PRODUCT_VERSION
              valueFrom:
                configMapKeyRef:
                  name: wren-config
                  key: WREN_PRODUCT_VERSION
            - name: IBIS_SERVER_ENDPOINT
              valueFrom:
                configMapKeyRef:
                  name: wren-config
                  key: IBIS_SERVER_ENDPOINT
          ports:
            - containerPort: 3000
