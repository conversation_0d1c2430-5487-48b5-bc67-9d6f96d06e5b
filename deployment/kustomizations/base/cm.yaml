apiVersion: v1
kind: ConfigMap
metadata:
  name: wren-config
data:
  # Wren Engine Service Port
  WREN_ENGINE_PORT: "8080"
  # Wren AI Service Port
  WREN_AI_SERVICE_PORT: "5555"

  WREN_UI_ENDPOINT: http://wren-ui-svc:3000

  #Release version used by wren ui https://github.com/Canner/WrenAI/blob/main/docker/docker-compose.yaml#L85-L88
  WREN_PRODUCT_VERSION: "0.12.0"
  WREN_ENGINE_VERSION: "0.12.3"
  WREN_AI_SERVICE_VERSION: "0.12.1"
  WREN_UI_VERSION: "0.17.6"

  # Document store related
  QDRANT_HOST: "wren-qdrant"

  # Telemetry
  POSTHOG_HOST: "https://app.posthog.com"
  TELEMETRY_ENABLED: "false"
  # this is for telemetry to know the model, i think ai-service might be able to provide a endpoint to get the information
  GENERATION_MODEL: "gpt-4o-mini-2024-07-18"

  # service endpoints of AI service & engine service
  WREN_ENGINE_ENDPOINT: "http://wren-engine-svc:8080"
  WREN_AI_ENDPOINT: "http://wren-ai-service-svc:5555"
  #WREN_AI_ENDPOINT: "http://wren-ai-service-svc.ai-system.svc.cluster.local:5555"

  # "pg" for postgres as UI application database
  WREN_UI_DB_TYPE: pg

  #For bootstrap
  WREN_ENGINE_DATA_PATH: "/app/data"

  ### if DB_TYPE = "postgres" you must provide PG_URL string in the *Secret* manifest file (deployment/kustomizations/examples/secret-wren_example.yaml) to connect to postgres

  #DEBUG, INFO
  LOGGING_LEVEL: INFO

  IBIS_SERVER_ENDPOINT: http://wren-ibis-server-svc:8000
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: wren-ai-service-config
data:
  config.yaml: |
    type: llm
    provider: litellm_llm
    timeout: 120
    models:
    - alias: default
      model: gpt-4.1-nano-2025-04-14
      context_window_size: 1000000
      kwargs:
        max_tokens: 4096
        n: 1
        seed: 0
        temperature: 0
    - model: gpt-4.1-mini-2025-04-14
      context_window_size: 1000000
      kwargs:
        max_tokens: 4096
        n: 1
        seed: 0
        temperature: 0
    - model: gpt-4.1-2025-04-14
      context_window_size: 1000000
      kwargs:
        max_tokens: 4096
        n: 1
        seed: 0
        temperature: 0

    ---
    type: embedder
    provider: litellm_embedder
    models:
    - model: text-embedding-3-large
      alias: default
      timeout: 120

    ---
    type: engine
    provider: wren_ui
    endpoint: http://wren-ui-svc:3000

    ---
    type: engine
    provider: wren_ibis
    endpoint: http://wren-ibis-server-svc:8000

    ---
    type: document_store
    provider: qdrant
    location: http://wren-qdrant:6333
    embedding_model_dim: 3072
    timeout: 120

    ---
    type: pipeline
    pipes:
      - name: db_schema_indexing
        embedder: litellm_embedder.default
        document_store: qdrant
      - name: historical_question_indexing
        embedder: litellm_embedder.default
        document_store: qdrant
      - name: table_description_indexing
        embedder: litellm_embedder.default
        document_store: qdrant
      - name: db_schema_retrieval
        llm: litellm_llm.default
        embedder: litellm_embedder.default
        document_store: qdrant
      - name: historical_question_retrieval
        embedder: litellm_embedder.default
        document_store: qdrant
      - name: sql_generation
        llm: litellm_llm.default
        engine: wren_ui
        document_store: qdrant
      - name: sql_correction
        llm: litellm_llm.default
        engine: wren_ui
        document_store: qdrant
      - name: followup_sql_generation
        llm: litellm_llm.default
        engine: wren_ui
        document_store: qdrant
      - name: sql_answer
        llm: litellm_llm.default
      - name: semantics_description
        llm: litellm_llm.default
      - name: relationship_recommendation
        llm: litellm_llm.default
        engine: wren_ui
      - name: question_recommendation
        llm: litellm_llm.default
      - name: question_recommendation_db_schema_retrieval
        llm: litellm_llm.default
        embedder: litellm_embedder.default
        document_store: qdrant
      - name: question_recommendation_sql_generation
        llm: litellm_llm.default
        engine: wren_ui
        document_store: qdrant
      - name: intent_classification
        llm: litellm_llm.default
        embedder: litellm_embedder.default
        document_store: qdrant
      - name: misleading_assistance
        llm: litellm_llm.default
      - name: data_assistance
        llm: litellm_llm.default
      - name: sql_pairs_indexing
        document_store: qdrant
        embedder: litellm_embedder.default
      - name: sql_pairs_retrieval
        document_store: qdrant
        embedder: litellm_embedder.default
        llm: litellm_llm.default
      - name: preprocess_sql_data
        llm: litellm_llm.default
      - name: sql_executor
        engine: wren_ui
      - name: chart_generation
        llm: litellm_llm.default
      - name: chart_adjustment
        llm: litellm_llm.default
      - name: user_guide_assistance
        llm: litellm_llm.default
      - name: sql_question_generation
        llm: litellm_llm.default
      - name: sql_generation_reasoning
        llm: litellm_llm.default
      - name: followup_sql_generation_reasoning
        llm: litellm_llm.default
      - name: sql_regeneration
        llm: litellm_llm.default
        engine: wren_ui
      - name: instructions_indexing
        embedder: litellm_embedder.default
        document_store: qdrant
      - name: instructions_retrieval
        embedder: litellm_embedder.default
        document_store: qdrant
      - name: sql_functions_retrieval
        engine: wren_ibis
        document_store: qdrant
      - name: project_meta_indexing
        document_store: qdrant
      - name: sql_tables_extraction
        llm: litellm_llm.default

    ---
    settings:
      doc_endpoint: https://docs.getwren.ai
      is_oss: true
      engine_timeout: 30
      column_indexing_batch_size: 50
      table_retrieval_size: 10
      table_column_retrieval_size: 100
      allow_intent_classification: true
      allow_sql_generation_reasoning: true
      allow_sql_functions_retrieval: true
      enable_column_pruning: false
      max_sql_correction_retries: 3
      query_cache_maxsize: 1000
      query_cache_ttl: 3600
      langfuse_host: https://cloud.langfuse.com
      langfuse_enable: true
      logging_level: DEBUG
      development: false
      historical_question_retrieval_similarity_threshold: 0.9
      sql_pairs_similarity_threshold: 0.7
      sql_pairs_retrieval_max_size: 10
      instructions_similarity_threshold: 0.7
      instructions_top_k: 10
