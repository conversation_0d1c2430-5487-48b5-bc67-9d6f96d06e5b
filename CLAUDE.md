# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

WrenAI is an open-source GenBI (Generative Business Intelligence) agent that enables users to query databases using natural language, generating accurate SQL queries, charts, and AI-powered insights.

## Essential Commands

### Wren UI (Frontend - Node.js/React)
```bash
cd wren-ui
yarn install           # Install dependencies
yarn dev              # Run development server
yarn build            # Build production
yarn test             # Run unit tests
yarn test:e2e         # Run E2E tests
yarn lint             # Run linting and type checking
yarn check-types      # TypeScript type checking only
yarn generate-gql     # Generate GraphQL types
```

### Wren AI Service (Python AI Service)
```bash
cd wren-ai-service
just init             # Initialize configuration
just start            # Start the service
just up               # Start development environment
just test             # Run tests
just test-usecases    # Run use case tests
```

### Wren Engine (Multiple components)
- **Ibis Server (Python)**: Use `pytest` with database-specific markers
- **Wren Core (Rust)**: Use `cargo build`, `cargo test`
- **Wren Core Legacy (Java)**: Use `./mvnw clean install`, `./mvnw test`

### Docker Development
```bash
cd docker
docker-compose -f docker-compose-dev.yaml --env-file .env.local up
```

## Architecture Overview

### Core Services
1. **Wren UI** (Port 3000): Next.js frontend & GraphQL API gateway
2. **Wren Engine** (Ports 8080, 7432): SQL processing core (Trino fork)
3. **Ibis Server** (Port 8000): Data source connection bridge
4. **Wren AI Service** (Port 5555): LLM integration for Text-to-SQL
5. **Qdrant** (Ports 6333, 6334): Vector database for embeddings
6. **Bootstrap Service**: System initialization

### Key Architectural Patterns
- **Microservices**: Each component runs independently
- **Pipeline Architecture**: Modular AI pipelines configurable via YAML
- **Semantic Layer**: MDL (Modeling Definition Language) abstracts database complexity
- **Communication**: Services communicate via REST APIs and GraphQL

### Data Flow
```
User Query → Wren UI → Wren AI Service → LLM Provider
                    ↘ Wren Engine → Ibis Server → Database
                    ↘ Qdrant (context retrieval)
```

## Development Guidelines

### Multi-Service Development
When working across multiple services:
1. Comment out services in `docker/docker-compose-dev.yml` that you'll run locally
2. Update `.env.local` to point to local services
3. Change `http://wren-ui:3000` to `http://host.docker.internal:3000` in `config.yaml`

### Adding New Data Sources
Requires changes in both Wren Engine and Wren UI:
1. **Engine**: Implement connector in ibis-server
2. **UI Backend**: Update `dataSource.ts`, `ibisAdaptor.ts`, `projectRepository.ts`
3. **UI Frontend**: Add logo, form template, update data source list

### Code Style
- **Python**: Uses `ruff` for linting and formatting
- **TypeScript**: Uses ESLint and TypeScript compiler
- **Rust**: Standard Cargo formatting
- **Java**: Maven with standard formatting

### Testing Approach
- **UI**: Jest for unit tests, Playwright for E2E
- **Python Services**: pytest with service-specific markers
- **Engine**: Database-specific test markers (e.g., `pytest -m athena`)

## Important Files and Locations

- **UI Database**: SQLite database stores projects, models, threads, dashboards
- **Vector Store**: Qdrant stores embeddings for semantic search
- **Configuration**: YAML files for pipeline and LLM provider configuration
- **MDL Models**: Define business logic and relationships